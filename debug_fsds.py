#!/usr/bin/env python3
"""
Debug FSDS query to understand why it's not finding document content
"""

import requests

def debug_fsds():
    """Debug the FSDS query."""
    
    query = "What is the full form of FSDS?"
    
    print(f"🔍 Debugging query: '{query}'")
    print("="*60)
    
    try:
        response = requests.post(
            "http://localhost:8000/api/query",
            json={
                "query": query,
                "model": "gemini-2.0-flash",
                "fallback_enabled": True,
                "extract_format": "paragraph",
                "use_hybrid_search": True
            },
            headers={"Content-Type": "application/json"},
            timeout=30
        )
        
        if response.status_code == 200:
            data = response.json()
            
            print("📊 Full Response Analysis:")
            doc_answer = data.get('document_answer') or ''
            web_answer = data.get('website_answer') or ''
            print(f"   📄 Document Answer: {bool(doc_answer.strip())}")
            print(f"   🌐 Website Answer: {bool(web_answer.strip())}")
            print(f"   🤖 LLM Fallback: {data.get('llmFallbackUsed', False)}")
            print(f"   📄 Document Sources: {len(data.get('document_sources', []))}")
            print(f"   🌐 Website Sources: {len(data.get('website_sources', []))}")
            print(f"   📊 Total Sources: {len(data.get('sources', []))}")
            
            # Show document sources details
            doc_sources = data.get('document_sources', [])
            if doc_sources:
                print(f"\n📄 Document Sources Details:")
                for i, source in enumerate(doc_sources, 1):
                    print(f"   {i}. File: {source.get('filename', 'Unknown')}")
                    print(f"      Page: {source.get('page', 'Unknown')}")
                    print(f"      Similarity: {source.get('similarity', 'Unknown')}")
            
            # Show website sources details  
            web_sources = data.get('website_sources', [])
            if web_sources:
                print(f"\n🌐 Website Sources Details:")
                for i, source in enumerate(web_sources, 1):
                    print(f"   {i}. URL: {source.get('url', 'Unknown')}")
                    print(f"      Similarity: {source.get('similarity', 'Unknown')}")
            
            # Show answers
            if data.get('document_answer'):
                print(f"\n📄 Document Answer:")
                print(f"   {data['document_answer'][:200]}...")
                
            if data.get('website_answer'):
                print(f"\n🌐 Website Answer:")
                print(f"   {data['website_answer'][:200]}...")
                
            if data.get('answer'):
                print(f"\n📝 Main Answer:")
                print(f"   {data['answer'][:200]}...")
                
        else:
            print(f"❌ HTTP Error: {response.status_code}")
            print(f"Response: {response.text}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

if __name__ == "__main__":
    debug_fsds()
