[{"C:\\IR App\\frontend\\src\\index.tsx": "1", "C:\\IR App\\frontend\\src\\reportWebVitals.ts": "2", "C:\\IR App\\frontend\\src\\AppRouter.tsx": "3", "C:\\IR App\\frontend\\src\\App.tsx": "4", "C:\\IR App\\frontend\\src\\pages\\DocumentViewer.tsx": "5", "C:\\IR App\\frontend\\src\\pages\\documents\\DocumentsPage.tsx": "6", "C:\\IR App\\frontend\\src\\pages\\websites\\WebsitesPage.tsx": "7", "C:\\IR App\\frontend\\src\\components\\layout\\Header.tsx": "8", "C:\\IR App\\frontend\\src\\pages\\settings\\SettingsPage.tsx": "9", "C:\\IR App\\frontend\\src\\services\\api.ts": "10", "C:\\IR App\\frontend\\src\\components\\ui\\LLMSelector.tsx": "11", "C:\\IR App\\frontend\\src\\components\\ui\\InteractiveAnswer.tsx": "12", "C:\\IR App\\frontend\\src\\components\\documents\\SimplePDFViewer.tsx": "13", "C:\\IR App\\frontend\\src\\components\\documents\\DocumentViewModal.tsx": "14", "C:\\IR App\\frontend\\src\\components\\documents\\DocumentUploadForm.tsx": "15", "C:\\IR App\\frontend\\src\\components\\websites\\WebsiteExtractForm.tsx": "16", "C:\\IR App\\frontend\\src\\components\\documents\\DocumentsTable.tsx": "17", "C:\\IR App\\frontend\\src\\components\\websites\\WebsitesTable.tsx": "18", "C:\\IR App\\frontend\\src\\components\\websites\\WebsiteViewModal.tsx": "19", "C:\\IR App\\frontend\\src\\components\\ui\\FeedbackEmailConfig.tsx": "20", "C:\\IR App\\frontend\\src\\services\\supabase.ts": "21", "C:\\IR App\\frontend\\src\\components\\ui\\FeedbackModal.tsx": "22", "C:\\IR App\\frontend\\src\\components\\documents\\PDFViewer.tsx": "23", "C:\\IR App\\frontend\\src\\components\\ui\\alert.tsx": "24", "C:\\IR App\\frontend\\src\\contexts\\ChatContext.tsx": "25", "C:\\IR App\\frontend\\src\\components\\chat\\ChatSidebar.tsx": "26", "C:\\IR App\\frontend\\src\\services\\categoryApi.ts": "27", "C:\\IR App\\frontend\\src\\components\\ui\\TrainLoader.tsx": "28", "C:\\IR App\\frontend\\src\\components\\ui\\VisualContent.tsx": "29", "C:\\IR App\\frontend\\src\\components\\categories\\ManageCategoriesModal.tsx": "30", "C:\\IR App\\frontend\\src\\components\\categories\\BulkCategoryImport.tsx": "31", "C:\\IR App\\frontend\\src\\components\\ui\\CategorySelectorModal.tsx": "32", "C:\\IR App\\frontend\\src\\utils\\supabaseClient.ts": "33"}, {"size": 519, "mtime": 1749185957120, "results": "34", "hashOfConfig": "35"}, {"size": 425, "mtime": 1746757926945, "results": "36", "hashOfConfig": "35"}, {"size": 1619, "mtime": 1749153117173, "results": "37", "hashOfConfig": "35"}, {"size": 40712, "mtime": 1750498820898, "results": "38", "hashOfConfig": "35"}, {"size": 10480, "mtime": 1749827002737, "results": "39", "hashOfConfig": "35"}, {"size": 17904, "mtime": 1749704248806, "results": "40", "hashOfConfig": "35"}, {"size": 15274, "mtime": 1749664553130, "results": "41", "hashOfConfig": "35"}, {"size": 2799, "mtime": 1748503610160, "results": "42", "hashOfConfig": "35"}, {"size": 18739, "mtime": 1750397016579, "results": "43", "hashOfConfig": "35"}, {"size": 44871, "mtime": 1750493736161, "results": "44", "hashOfConfig": "35"}, {"size": 5790, "mtime": 1749138428411, "results": "45", "hashOfConfig": "35"}, {"size": 5431, "mtime": 1748489621678, "results": "46", "hashOfConfig": "35"}, {"size": 4449, "mtime": 1748082417413, "results": "47", "hashOfConfig": "35"}, {"size": 9890, "mtime": 1749633427378, "results": "48", "hashOfConfig": "35"}, {"size": 35769, "mtime": 1749548106395, "results": "49", "hashOfConfig": "35"}, {"size": 32110, "mtime": 1749190887473, "results": "50", "hashOfConfig": "35"}, {"size": 28313, "mtime": 1749159324509, "results": "51", "hashOfConfig": "35"}, {"size": 15364, "mtime": 1749159616136, "results": "52", "hashOfConfig": "35"}, {"size": 8113, "mtime": 1746848396668, "results": "53", "hashOfConfig": "35"}, {"size": 5634, "mtime": 1748333597589, "results": "54", "hashOfConfig": "35"}, {"size": 15482, "mtime": 1750498627386, "results": "55", "hashOfConfig": "35"}, {"size": 5354, "mtime": 1748489621678, "results": "56", "hashOfConfig": "35"}, {"size": 6102, "mtime": 1749704240487, "results": "57", "hashOfConfig": "35"}, {"size": 1514, "mtime": 1748265464172, "results": "58", "hashOfConfig": "35"}, {"size": 5260, "mtime": 1748339426726, "results": "59", "hashOfConfig": "35"}, {"size": 14432, "mtime": 1749913780975, "results": "60", "hashOfConfig": "35"}, {"size": 18720, "mtime": 1749162314029, "results": "61", "hashOfConfig": "35"}, {"size": 6360, "mtime": 1748339169110, "results": "62", "hashOfConfig": "35"}, {"size": 18504, "mtime": 1750346411780, "results": "63", "hashOfConfig": "35"}, {"size": 28825, "mtime": 1749158790511, "results": "64", "hashOfConfig": "35"}, {"size": 14327, "mtime": 1749154997884, "results": "65", "hashOfConfig": "35"}, {"size": 14292, "mtime": 1749187165019, "results": "66", "hashOfConfig": "35"}, {"size": 1192, "mtime": 1749882204915, "results": "67", "hashOfConfig": "35"}, {"filePath": "68", "messages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, "ngactd", {"filePath": "71", "messages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "73", "messages": "74", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "75", "messages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "79", "messages": "80", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "81", "messages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "83", "messages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "85", "messages": "86", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "87", "messages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "89", "messages": "90", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "91", "messages": "92", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "93", "messages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "95", "messages": "96", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "97", "messages": "98", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "99", "messages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "101", "messages": "102", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "103", "messages": "104", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "105", "messages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "107", "messages": "108", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "109", "messages": "110", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "111", "messages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "113", "messages": "114", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "115", "messages": "116", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "117", "messages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "119", "messages": "120", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "121", "messages": "122", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "123", "messages": "124", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "125", "messages": "126", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 10, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "127", "usedDeprecatedRules": "70"}, {"filePath": "128", "messages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "130", "usedDeprecatedRules": "70"}, {"filePath": "131", "messages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "133", "messages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "70"}, {"filePath": "135", "messages": "136", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": "137", "usedDeprecatedRules": "70"}, "C:\\IR App\\frontend\\src\\index.tsx", [], ["138", "139"], "C:\\IR App\\frontend\\src\\reportWebVitals.ts", [], "C:\\IR App\\frontend\\src\\AppRouter.tsx", [], "C:\\IR App\\frontend\\src\\App.tsx", [], "C:\\IR App\\frontend\\src\\pages\\DocumentViewer.tsx", [], "C:\\IR App\\frontend\\src\\pages\\documents\\DocumentsPage.tsx", [], "C:\\IR App\\frontend\\src\\pages\\websites\\WebsitesPage.tsx", [], "C:\\IR App\\frontend\\src\\components\\layout\\Header.tsx", [], "C:\\IR App\\frontend\\src\\pages\\settings\\SettingsPage.tsx", [], "C:\\IR App\\frontend\\src\\services\\api.ts", [], "C:\\IR App\\frontend\\src\\components\\ui\\LLMSelector.tsx", [], "C:\\IR App\\frontend\\src\\components\\ui\\InteractiveAnswer.tsx", [], "C:\\IR App\\frontend\\src\\components\\documents\\SimplePDFViewer.tsx", [], "C:\\IR App\\frontend\\src\\components\\documents\\DocumentViewModal.tsx", [], "C:\\IR App\\frontend\\src\\components\\documents\\DocumentUploadForm.tsx", [], "C:\\IR App\\frontend\\src\\components\\websites\\WebsiteExtractForm.tsx", [], "C:\\IR App\\frontend\\src\\components\\documents\\DocumentsTable.tsx", [], "C:\\IR App\\frontend\\src\\components\\websites\\WebsitesTable.tsx", [], "C:\\IR App\\frontend\\src\\components\\websites\\WebsiteViewModal.tsx", [], "C:\\IR App\\frontend\\src\\components\\ui\\FeedbackEmailConfig.tsx", [], "C:\\IR App\\frontend\\src\\services\\supabase.ts", ["140"], "C:\\IR App\\frontend\\src\\components\\ui\\FeedbackModal.tsx", [], "C:\\IR App\\frontend\\src\\components\\documents\\PDFViewer.tsx", [], "C:\\IR App\\frontend\\src\\components\\ui\\alert.tsx", [], "C:\\IR App\\frontend\\src\\contexts\\ChatContext.tsx", [], "C:\\IR App\\frontend\\src\\components\\chat\\ChatSidebar.tsx", [], "C:\\IR App\\frontend\\src\\services\\categoryApi.ts", [], "C:\\IR App\\frontend\\src\\components\\ui\\TrainLoader.tsx", [], "C:\\IR App\\frontend\\src\\components\\ui\\VisualContent.tsx", ["141", "142", "143", "144", "145", "146", "147", "148", "149", "150"], "import React, { useState, useEffect } from 'react';\n\ninterface VisualContentProps {\n  source: {\n    content_type?: string;\n    visual_content?: Record<string, any>;\n    storage_url?: string;\n    display_type?: string;\n    filename?: string;\n    page?: number;\n  };\n}\n\nconst VisualContent: React.FC<VisualContentProps> = ({ source }) => {\n  const [selectedTab, setSelectedTab] = useState<string>('content');\n  const [imageLoading, setImageLoading] = useState<boolean>(true);\n  const [imageError, setImageError] = useState<boolean>(false);\n  const [imageTries, setImageTries] = useState<number>(0);\n  const [cacheBuster, setCacheBuster] = useState<string>(`cb-${Date.now()}`);\n  const [visibleDebug, setVisibleDebug] = useState<boolean>(false);\n  \n  // Refresh image on source change\n  useEffect(() => {\n    // Reset state when source changes\n    setImageLoading(true);\n    setImageError(false);\n    setImageTries(0);\n    setCacheBuster(`cb-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`);\n    \n    console.log('🔄 Visual content source changed:', {\n      content_type: source.content_type,\n      filename: source.filename,\n      page: source.page,\n      has_visual_content: !!source.visual_content,\n      storage_url: source.storage_url ? `${source.storage_url.substring(0, 30)}...` : 'None',\n    });\n  }, [source.filename, source.page, source.storage_url]);\n\n  if (!source.content_type || source.content_type === 'text') {\n    return null; // No visual content to display\n  }\n\n  const renderTableContent = () => {\n    const visualContent = source.visual_content;\n    \n    if (!visualContent) return null;\n\n    // 0. Highest priority: raw HTML table (pre-rendered in backend)\n    if (visualContent.table_html) {\n      return (\n        <div className=\"overflow-x-auto w-full\">\n          <div\n            className=\"prose prose-sm max-w-none\"\n            dangerouslySetInnerHTML={{ __html: visualContent.table_html }}\n          />\n        </div>\n      );\n    }\n\n    // 1. If we have table data, render it as a proper table\n    if (visualContent.table_data && Array.isArray(visualContent.table_data)) {\n      const tableData = visualContent.table_data;\n      if (tableData.length === 0) return null;\n\n      const headers = tableData[0] || [];\n      const rows = tableData.slice(1);\n\n      return (\n        <div className=\"overflow-x-auto w-full\">\n          <table className=\"min-w-full bg-white border border-gray-200 rounded-lg table-fixed\">\n            <thead className=\"bg-gray-50\">\n              <tr>\n                {headers.map((header: any, idx: number) => (\n                  <th \n                    key={idx}\n                    className=\"px-3 py-2 text-left text-sm font-semibold text-gray-700 border-b border-gray-200\"\n                    style={{ minWidth: '100px', maxWidth: '200px' }}\n                  >\n                    {String(header || '').trim() || `Column ${idx + 1}`}\n                  </th>\n                ))}\n              </tr>\n            </thead>\n            <tbody>\n              {rows.map((row: any[], rowIdx: number) => (\n                <tr key={rowIdx} className=\"hover:bg-gray-50 transition-colors\">\n                  {headers.map((_: any, cellIdx: number) => (\n                    <td \n                      key={cellIdx}\n                      className=\"px-3 py-2 text-sm text-gray-700 border-b border-gray-100 truncate\"\n                      title={String(row[cellIdx] || '')}\n                    >\n                      {String(row[cellIdx] || '').trim() || '-'}\n                    </td>\n                  ))}\n                </tr>\n              ))}\n            </tbody>\n          </table>\n        </div>\n      );\n    }\n    \n    // 2. If we have markdown table, render it as HTML\n    if (!visualContent.table_html && visualContent.markdown_table) {\n      return (\n        <div className=\"overflow-x-auto w-full\">\n          <div className=\"bg-white rounded-lg border border-gray-200\">\n            <div \n              className=\"prose prose-sm max-w-none p-0\"\n              dangerouslySetInnerHTML={{\n                __html: markdownTableToHtml(visualContent.markdown_table)\n              }}\n            />\n          </div>\n        </div>\n      );\n    }\n\n    // Fallback: Try to display as text table if available\n    if (visualContent.text_table) {\n      return (\n        <div className=\"bg-white rounded-lg border border-gray-200 p-4\">\n          <pre className=\"text-sm text-gray-700 whitespace-pre-wrap font-mono\">\n            {visualContent.text_table}\n          </pre>\n        </div>\n      );\n    }\n\n    return (\n      <div className=\"bg-gray-50 rounded-lg p-4 text-sm text-gray-600\">\n        Table data is not available in a displayable format.\n        {visualContent && (\n          <div className=\"mt-2 text-xs\">\n            Available data: {Object.keys(visualContent).join(', ')}\n          </div>\n        )}\n      </div>\n    );\n  };\n\n  const getImageSources = () => {\n    const visualContent = source.visual_content;\n    const sources = [];\n    \n    // 1. Try storage URL if available (with cache buster)\n    if (source.storage_url) {\n      const hasQueryParams = source.storage_url.includes('?');\n      const cacheParam = hasQueryParams ? `&_cb=${cacheBuster}` : `?_cb=${cacheBuster}`;\n      sources.push({\n        url: `${source.storage_url}${cacheParam}`,\n        type: 'Storage URL'\n      });\n    }\n    \n    // 2. Try base64 data if available\n    if (visualContent?.base64_data) {\n      sources.push({\n        url: `data:image/png;base64,${visualContent.base64_data}`,\n        type: 'Base64 Data'\n      });\n    }\n    \n    // 3. Generate path based on filename and page\n    if (source.filename && source.page !== undefined) {\n      const safeName = source.filename.replace(/[^a-zA-Z0-9]/g, '_').toLowerCase();\n      sources.push({\n        url: `/images/${safeName}_page${source.page}.png?_cb=${cacheBuster}`,\n        type: 'Generated Path'\n      });\n    }\n    \n    // 4. Logo as absolute fallback\n    sources.push({\n      url: `/logo512.png?_cb=${cacheBuster}`,\n      type: 'Default Logo'\n    });\n    \n    return sources;\n  };\n\n  const renderImageContent = () => {\n    const visualContent = source.visual_content;\n    \n    console.log('DEBUG: Rendering image for', source.filename, 'page', source.page);\n    console.log('DEBUG: storage_url =', source.storage_url);\n    console.log('DEBUG: base64_data available =', !!visualContent?.base64_data);\n    \n    // Show debug info with image\n    const debugInfo = (\n      <div className=\"bg-blue-50 p-2 mb-2 rounded text-xs\">\n        <p><strong>File:</strong> {source.filename || 'Unknown'}</p>\n        <p><strong>Page:</strong> {source.page || 'Unknown'}</p>\n        <p><strong>URL:</strong> {source.storage_url ? 'Available' : 'Not available'}</p>\n        <p><strong>Time:</strong> {new Date().toISOString()}</p>\n      </div>\n    );\n\n    // If we have explicit images array, render them all in a simple gallery\n    if (visualContent?.images && Array.isArray(visualContent.images) && visualContent.images.length > 0) {\n      return (\n        <div className=\"space-y-2\">\n          {visualContent.images.map((img: any, idx: number) => {\n            const imgUrl = typeof img === 'string'\n              ? img\n              : img.storage_url || (img.base64_data ? `data:image/png;base64,${img.base64_data}` : undefined);\n            if (!imgUrl) return null;\n            return (\n              <img\n                key={idx}\n                src={`${imgUrl}${imgUrl.includes('?') ? '&' : '?'}_cb=${cacheBuster}`}\n                alt={`Image ${idx + 1} from page ${source.page}`}\n                className=\"max-w-full h-auto rounded-lg border border-gray-200 shadow-md\"\n              />\n            );\n          })}\n        </div>\n      );\n    }\n\n    // Fallback to storage_url or base64_data as before\n    if (source.storage_url) {\n      return (\n        <div className=\"relative\">\n          {debugInfo}\n          {imageLoading && (\n            <div className=\"absolute inset-0 flex items-center justify-center bg-gray-100 rounded-lg\">\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500\"></div>\n            </div>\n          )}\n          <img\n            src={`${source.storage_url}?t=${Date.now()}`}\n            alt={`Image from page ${source.page} of ${source.filename}`}\n            className={`max-w-full h-auto rounded-lg border border-gray-200 ${imageLoading ? 'opacity-0' : 'opacity-100'}`}\n            onLoad={() => setImageLoading(false)}\n            onError={() => {\n              console.error('DEBUG: Storage URL image failed to load:', source.storage_url);\n              setImageLoading(false);\n              setImageError(true);\n            }}\n          />\n        </div>\n      );\n    }\n    // base64 fallback\n    if (visualContent?.base64_data) {\n      return (\n        <img src={`data:image/png;base64,${visualContent.base64_data}`} alt=\"Embedded\" className=\"max-w-full h-auto rounded-lg border\" />\n      );\n    }\n\n    // Default to logo if no other image is available\n    return (\n      <div className=\"bg-gray-50 rounded-lg p-4 text-center\">\n        {debugInfo}\n        <p className=\"text-sm text-gray-600 mb-2\">No image content available from the document</p>\n        <img \n          src={`/logo512.png?t=${Date.now()}`}\n          alt=\"Default logo\"\n          className=\"max-w-full h-auto rounded-lg border border-gray-200 mx-auto\"\n          style={{ maxHeight: '200px' }}\n        />\n      </div>\n    );\n  };\n\n  const renderChartContent = () => {\n    const visualContent = source.visual_content;\n    \n    if (!visualContent) return null;\n\n    return (\n      <div className=\"bg-gradient-to-br from-purple-50 to-indigo-50 rounded-lg p-4 border border-purple-200\">\n        <h4 className=\"font-semibold text-purple-800 mb-3 flex items-center\">\n          📊 Chart/Diagram Detected\n        </h4>\n        <div className=\"space-y-3\">\n          {visualContent.description && (\n            <p className=\"text-sm text-gray-700\">{visualContent.description}</p>\n          )}\n          <div className=\"grid grid-cols-2 gap-4 text-xs text-gray-600\">\n            {visualContent.drawing_count && (\n              <div>\n                <span className=\"font-medium\">Drawing Elements:</span> {visualContent.drawing_count}\n              </div>\n            )}\n            {visualContent.confidence && (\n              <div>\n                <span className=\"font-medium\">Confidence:</span> {visualContent.confidence}\n              </div>\n            )}\n            {visualContent.has_lines && (\n              <div className=\"flex items-center\">\n                <span className=\"w-2 h-2 bg-green-400 rounded-full mr-2\"></span>\n                Contains lines\n              </div>\n            )}\n            {visualContent.has_curves && (\n              <div className=\"flex items-center\">\n                <span className=\"w-2 h-2 bg-blue-400 rounded-full mr-2\"></span>\n                Contains curves\n              </div>\n            )}\n          </div>\n        </div>\n      </div>\n    );\n  };\n\n  const getContentTypeLabel = () => {\n    switch (source.content_type) {\n      case 'table': return '📋 Table';\n      case 'image': return '🖼️ Image';\n      case 'chart_diagram': return '📊 Chart/Diagram';\n      default: return '📄 Content';\n    }\n  };\n\n  const getContentTypeColor = () => {\n    switch (source.content_type) {\n      case 'table': return 'from-green-50 to-emerald-50 border-green-200';\n      case 'image': return 'from-blue-50 to-cyan-50 border-blue-200';\n      case 'chart_diagram': return 'from-purple-50 to-indigo-50 border-purple-200';\n      default: return 'from-gray-50 to-slate-50 border-gray-200';\n    }\n  };\n\n  return (\n    <div className={`bg-gradient-to-br ${getContentTypeColor()} rounded-lg border p-4 mt-3`}>\n      <div className=\"flex items-center justify-between mb-3\">\n        <h4 className=\"font-semibold text-gray-800 text-sm flex items-center\">\n          {getContentTypeLabel()}\n          {source.page && (\n            <span className=\"ml-2 text-xs bg-white bg-opacity-70 px-2 py-1 rounded\">\n              Page {source.page}\n            </span>\n          )}\n        </h4>\n        \n        {/* Tab selector for complex content */}\n        {source.content_type === 'table' && source.visual_content?.metadata && (\n          <div className=\"flex text-xs bg-white bg-opacity-50 rounded\">\n            <button\n              onClick={() => setSelectedTab('content')}\n              className={`px-2 py-1 rounded-l ${\n                selectedTab === 'content' ? 'bg-white text-gray-800' : 'text-gray-600'\n              }`}\n            >\n              Table\n            </button>\n            <button\n              onClick={() => setSelectedTab('metadata')}\n              className={`px-2 py-1 rounded-r ${\n                selectedTab === 'metadata' ? 'bg-white text-gray-800' : 'text-gray-600'\n              }`}\n            >\n              Info\n            </button>\n          </div>\n        )}\n      </div>\n\n      {/* Content display */}\n      {selectedTab === 'content' && (\n        <div>\n          {source.content_type === 'table' && renderTableContent()}\n          {source.content_type === 'image' && renderImageContent()}\n          {source.content_type === 'chart_diagram' && renderChartContent()}\n        </div>\n      )}\n\n      {/* Metadata tab */}\n      {selectedTab === 'metadata' && source.visual_content && (\n        <div className=\"bg-white bg-opacity-50 rounded p-3 text-xs text-gray-600\">\n          <div className=\"grid grid-cols-2 gap-2\">\n            <div><span className=\"font-medium\">Extraction Method:</span> {source.visual_content.extraction_method}</div>\n            <div><span className=\"font-medium\">Page:</span> {source.page}</div>\n            {source.visual_content.table_index !== undefined && (\n              <div><span className=\"font-medium\">Table Index:</span> {source.visual_content.table_index}</div>\n            )}\n            {source.visual_content.image_index !== undefined && (\n              <div><span className=\"font-medium\">Image Index:</span> {source.visual_content.image_index}</div>\n            )}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n};\n\n// Helper function to convert markdown table to HTML\nconst markdownTableToHtml = (markdown: string): string => {\n  if (!markdown || typeof markdown !== 'string') {\n    return `<div class=\"p-4 text-gray-600\">Invalid table data</div>`;\n  }\n  \n  const lines = markdown.split('\\n').filter(line => line.trim());\n  if (lines.length < 2) return `<div class=\"p-4 text-gray-600\">Invalid table format</div>`;\n\n  let html = '<table class=\"min-w-full border border-gray-200 rounded-lg\">';\n  \n  try {\n    // Process header\n    const headerLine = lines[0];\n    const headerCells = headerLine.split('|')\n      .map(cell => cell.trim())\n      .filter(cell => cell !== ''); // Remove empty cells from start/end\n    \n    if (headerCells.length === 0) {\n      // Try alternate format where cells are separated by multiple spaces\n      const spaceSeparatedCells = headerLine.split(/\\s{2,}/).filter(cell => cell.trim());\n      if (spaceSeparatedCells.length > 0) {\n        html += '<thead class=\"bg-gray-50\"><tr>';\n        spaceSeparatedCells.forEach((cell, index) => {\n          const cleanCell = cell.replace(/\\*\\*/g, '').trim();\n          html += `<th class=\"px-3 py-2 text-left text-sm font-semibold text-gray-700 border-b border-gray-200\">${cleanCell || `Column ${index + 1}`}</th>`;\n        });\n        html += '</tr></thead>';\n        \n        // Process data rows (skip separator row)\n        html += '<tbody>';\n        for (let i = 2; i < lines.length; i++) {\n          const rowCells = lines[i].split(/\\s{2,}/).filter(cell => cell.trim());\n          if (rowCells.length === 0) continue;\n          \n          html += '<tr class=\"hover:bg-gray-50 transition-colors\">';\n          // Make sure we have the right number of cells\n          while (rowCells.length < spaceSeparatedCells.length) {\n            rowCells.push('');\n          }\n          \n          spaceSeparatedCells.forEach((_, index) => {\n            const cellContent = rowCells[index] || '';\n            html += `<td class=\"px-3 py-2 text-sm text-gray-700 border-b border-gray-100\">${cellContent.trim() || '-'}</td>`;\n          });\n          html += '</tr>';\n        }\n        html += '</tbody></table>';\n        return html;\n      }\n      \n      return `<div class=\"p-4 text-gray-600\">No table headers found</div>`;\n    }\n    \n    html += '<thead class=\"bg-gray-50\"><tr>';\n    headerCells.forEach((cell, index) => {\n      const cleanCell = cell.replace(/\\*\\*/g, '').trim(); // Remove markdown bold\n      html += `<th class=\"px-3 py-2 text-left text-sm font-semibold text-gray-700 border-b border-gray-200\">${cleanCell || `Column ${index + 1}`}</th>`;\n    });\n    html += '</tr></thead>';\n\n    // Check if we have a separator row at index 1 (standard markdown table)\n    const hasSeparator = lines.length > 1 && lines[1].includes('|') && lines[1].includes('-');\n    const startRowIndex = hasSeparator ? 2 : 1;\n\n    // Process data rows\n    html += '<tbody>';\n    for (let i = startRowIndex; i < lines.length; i++) {\n      const rowLine = lines[i];\n      if (!rowLine.includes('|')) continue; // Skip non-table lines\n      \n      const cells = rowLine.split('|')\n        .map(cell => cell.trim())\n        .filter((cell, index, array) => {\n          // Keep all cells except first and last if they're empty (markdown format)\n          if (index === 0 || index === array.length - 1) {\n            return cell !== '';\n          }\n          return true;\n        });\n      \n      // Ensure we have the right number of cells\n      while (cells.length < headerCells.length) {\n        cells.push('');\n      }\n      \n      html += '<tr class=\"hover:bg-gray-50 transition-colors\">';\n      headerCells.forEach((_, cellIndex) => {\n        const cellContent = cells[cellIndex] || '';\n        const cleanCell = cellContent.replace(/\\*\\*/g, '').trim(); // Remove markdown bold\n        html += `<td class=\"px-3 py-2 text-sm text-gray-700 border-b border-gray-100\">${cleanCell || '-'}</td>`;\n      });\n      html += '</tr>';\n    }\n    html += '</tbody></table>';\n  } catch (error) {\n    console.error('Error parsing markdown table:', error);\n    const errorMessage = error instanceof Error ? error.message : 'Unknown error';\n    return `<div class=\"p-4 text-gray-600 bg-red-50 border border-red-200 rounded\">Error parsing table: ${errorMessage}</div>`;\n  }\n\n  return html;\n};\n\nexport default VisualContent; ", "C:\\IR App\\frontend\\src\\components\\categories\\ManageCategoriesModal.tsx", ["151", "152", "153"], "import React, { useState, useEffect, useCallback } from 'react';\r\nimport { X, Plus, Edit2, Trash2, ChevronDown, ChevronRight, FolderPlus, Save, AlertTriangle, Check, Settings, Upload } from 'lucide-react';\r\nimport { CategoryHierarchy, CategoryCreate, CategoryUpdate } from '../../types/documents';\r\nimport { \r\n  getDocumentCategoriesWithStats, \r\n  getWebsiteCategoriesWithStats,\r\n  createCategory,\r\n  createWebsiteCategory,\r\n  updateCategory,\r\n  deleteCategory,\r\n  deleteWebsiteCategory,\r\n  getCategoryHierarchy\r\n} from '../../services/categoryApi';\r\nimport BulkCategoryImport from './BulkCategoryImport';\r\n\r\ninterface ManageCategoriesModalProps {\r\n  isOpen: boolean;\r\n  onClose: () => void;\r\n  type: 'document' | 'website';\r\n  onCategoryUpdated?: () => void;\r\n}\r\n\r\ninterface CategoryNode extends CategoryHierarchy {\r\n  expanded?: boolean;\r\n  editing?: boolean;\r\n  children: CategoryNode[];\r\n}\r\n\r\ninterface CategoryStats {\r\n  total_count: number;\r\n  max_depth: number;\r\n  statistics: {\r\n    main_categories: number;\r\n    categories: number;\r\n    sub_categories: number;\r\n    minor_categories: number;\r\n    active: number;\r\n    inactive: number;\r\n  };\r\n}\r\n\r\ninterface DeleteConfirmation {\r\n  isOpen: boolean;\r\n  category: CategoryNode | null;\r\n  hasChildren: boolean;\r\n  linkedCount: number;\r\n}\r\n\r\nconst ManageCategoriesModal: React.FC<ManageCategoriesModalProps> = ({\r\n  isOpen,\r\n  onClose,\r\n  type,\r\n  onCategoryUpdated\r\n}) => {\r\n  // State management\r\n  const [categories, setCategories] = useState<CategoryNode[]>([]);\r\n  const [stats, setStats] = useState<CategoryStats | null>(null);\r\n  const [loading, setLoading] = useState(false);\r\n  const [error, setError] = useState<string | null>(null);\r\n  const [success, setSuccess] = useState<string | null>(null);\r\n\r\n  // Category operations state\r\n  const [expandedNodes, setExpandedNodes] = useState<Set<string>>(new Set());\r\n  const [editingCategory, setEditingCategory] = useState<string | null>(null);\r\n  const [editingName, setEditingName] = useState('');\r\n  const [editingDescription, setEditingDescription] = useState('');\r\n\r\n  // Create new category state\r\n  const [showCreateForm, setShowCreateForm] = useState(false);\r\n  const [createParentId, setCreateParentId] = useState<string | null>(null);\r\n  const [createLevel, setCreateLevel] = useState<number>(1);\r\n  const [newCategoryName, setNewCategoryName] = useState('');\r\n  const [newCategoryDescription, setNewCategoryDescription] = useState('');\r\n\r\n  // Delete confirmation state\r\n  const [deleteConfirmation, setDeleteConfirmation] = useState<DeleteConfirmation>({\r\n    isOpen: false,\r\n    category: null,\r\n    hasChildren: false,\r\n    linkedCount: 0\r\n  });\r\n\r\n  // Bulk import state\r\n  const [showBulkImport, setShowBulkImport] = useState(false);\r\n\r\n  // Load categories when modal opens\r\n  useEffect(() => {\r\n    if (isOpen) {\r\n      loadCategories();\r\n    }\r\n  }, [isOpen, type]);\r\n\r\n  // Auto-clear messages\r\n  useEffect(() => {\r\n    if (success) {\r\n      const timer = setTimeout(() => setSuccess(null), 3000);\r\n      return () => clearTimeout(timer);\r\n    }\r\n  }, [success]);\r\n\r\n  useEffect(() => {\r\n    if (error) {\r\n      const timer = setTimeout(() => setError(null), 5000);\r\n      return () => clearTimeout(timer);\r\n    }\r\n  }, [error]);\r\n\r\n  const loadCategories = async () => {\r\n    try {\r\n      setLoading(true);\r\n      setError(null);\r\n\r\n      console.log(`Loading categories for type: ${type}`);\r\n      const response = type === 'document' \r\n        ? await getDocumentCategoriesWithStats()\r\n        : await getWebsiteCategoriesWithStats();\r\n      \r\n      console.log('Loaded categories response:', response);\r\n\r\n      // Transform categories to include UI state\r\n      const transformedCategories = transformCategoriesToNodes(response.categories);\r\n      \r\n      setCategories(transformedCategories);\r\n      setStats({\r\n        total_count: response.total_count,\r\n        max_depth: response.max_depth,\r\n        statistics: response.statistics\r\n      });\r\n\r\n      // Auto-expand first level\r\n      const firstLevelIds = transformedCategories.map(cat => cat.id);\r\n      setExpandedNodes(new Set(firstLevelIds));\r\n\r\n    } catch (err: any) {\r\n      console.error('Error loading categories:', err);\r\n      setError(err.message || 'Failed to load categories');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const transformCategoriesToNodes = (cats: CategoryHierarchy[]): CategoryNode[] => {\r\n    const transform = (category: CategoryHierarchy): CategoryNode => ({\r\n      ...category,\r\n      expanded: false,\r\n      editing: false,\r\n      children: category.children ? category.children.map(transform) : []\r\n    });\r\n\r\n    return cats.map(transform);\r\n  };\r\n\r\n  const toggleExpanded = (categoryId: string) => {\r\n    setExpandedNodes(prev => {\r\n      const newSet = new Set(prev);\r\n      if (newSet.has(categoryId)) {\r\n        newSet.delete(categoryId);\r\n      } else {\r\n        newSet.add(categoryId);\r\n      }\r\n      return newSet;\r\n    });\r\n  };\r\n\r\n  const startEdit = (category: CategoryNode) => {\r\n    setEditingCategory(category.id);\r\n    setEditingName(category.name);\r\n    setEditingDescription(category.description || '');\r\n  };\r\n\r\n  const cancelEdit = () => {\r\n    setEditingCategory(null);\r\n    setEditingName('');\r\n    setEditingDescription('');\r\n  };\r\n\r\n  const saveEdit = async () => {\r\n    if (!editingCategory || !editingName.trim()) return;\r\n\r\n    try {\r\n      setLoading(true);\r\n      const updates: CategoryUpdate = {\r\n        name: editingName.trim(),\r\n        description: editingDescription.trim() || undefined\r\n      };\r\n\r\n      await updateCategory(editingCategory, updates);\r\n      \r\n      setSuccess('Category updated successfully');\r\n      cancelEdit();\r\n      loadCategories();\r\n      onCategoryUpdated?.();\r\n\r\n    } catch (err: any) {\r\n      setError(err.message || 'Failed to update category');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const startCreate = (parentId: string | null = null, level: number = 1) => {\r\n    console.log(`startCreate called with parentId: ${parentId}, level: ${level}`);\r\n    setCreateParentId(parentId);\r\n    setCreateLevel(level);\r\n    setNewCategoryName('');\r\n    setNewCategoryDescription('');\r\n    setShowCreateForm(true);\r\n  };\r\n\r\n  const cancelCreate = () => {\r\n    setShowCreateForm(false);\r\n    setCreateParentId(null);\r\n    setNewCategoryName('');\r\n    setNewCategoryDescription('');\r\n  };\r\n\r\n  const createNewCategory = async () => {\r\n    if (!newCategoryName.trim()) return;\r\n\r\n    try {\r\n      setLoading(true);\r\n      \r\n      // Determine category type based on level\r\n      const typeMap = {\r\n        1: 'main_category',\r\n        2: 'category', \r\n        3: 'sub_category',\r\n        4: 'minor_category'\r\n      };\r\n\r\n      console.log('Creating category with level:', createLevel, 'parent:', createParentId);\r\n      console.log('Type mapping result:', typeMap[createLevel as keyof typeof typeMap]);\r\n\r\n      const newCategory: CategoryCreate = {\r\n        name: newCategoryName.trim(),\r\n        type: typeMap[createLevel as keyof typeof typeMap] as 'main_category' | 'category' | 'sub_category' | 'minor_category',\r\n        parent_id: createParentId || undefined,\r\n        description: newCategoryDescription.trim() || undefined,\r\n        sort_order: 0\r\n      };\r\n\r\n      console.log('Creating category:', newCategory);\r\n\r\n      if (type === 'document') {\r\n        await createCategory(newCategory);\r\n      } else {\r\n        await createWebsiteCategory(newCategory);\r\n      }\r\n\r\n      setSuccess('Category created successfully');\r\n      cancelCreate();\r\n      loadCategories();\r\n      onCategoryUpdated?.();\r\n\r\n    } catch (err: any) {\r\n      console.error('Category creation error:', err);\r\n      setError(err.message || 'Failed to create category');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const startDelete = async (category: CategoryNode) => {\r\n    // Check if category has children or linked items\r\n    const hasChildren = category.children && category.children.length > 0;\r\n    \r\n    // TODO: Get actual linked count from API\r\n    const linkedCount = 0; // This would come from checking documents/websites\r\n\r\n    setDeleteConfirmation({\r\n      isOpen: true,\r\n      category,\r\n      hasChildren: hasChildren || false,\r\n      linkedCount\r\n    });\r\n  };\r\n\r\n  const confirmDelete = async () => {\r\n    if (!deleteConfirmation.category) return;\r\n\r\n    try {\r\n      setLoading(true);\r\n      \r\n      if (type === 'document') {\r\n        await deleteCategory(deleteConfirmation.category.id, false);\r\n      } else {\r\n        await deleteWebsiteCategory(deleteConfirmation.category.id, false);\r\n      }\r\n\r\n      setSuccess('Category deleted successfully');\r\n      setDeleteConfirmation({ isOpen: false, category: null, hasChildren: false, linkedCount: 0 });\r\n      loadCategories();\r\n      onCategoryUpdated?.();\r\n\r\n    } catch (err: any) {\r\n      setError(err.message || 'Failed to delete category');\r\n    } finally {\r\n      setLoading(false);\r\n    }\r\n  };\r\n\r\n  const cancelDelete = () => {\r\n    setDeleteConfirmation({ isOpen: false, category: null, hasChildren: false, linkedCount: 0 });\r\n  };\r\n\r\n  const handleBulkImportComplete = () => {\r\n    setShowBulkImport(false);\r\n    loadCategories();\r\n    onCategoryUpdated?.();\r\n  };\r\n\r\n  // Expand all categories recursively\r\n  const expandAllCategories = () => {\r\n    const getAllCategoryIds = (cats: CategoryNode[]): string[] => {\r\n      const ids: string[] = [];\r\n      cats.forEach(cat => {\r\n        ids.push(cat.id);\r\n        if (cat.children && cat.children.length > 0) {\r\n          ids.push(...getAllCategoryIds(cat.children));\r\n        }\r\n      });\r\n      return ids;\r\n    };\r\n    \r\n    const allIds = getAllCategoryIds(categories);\r\n    setExpandedNodes(new Set(allIds));\r\n  };\r\n\r\n  // Collapse all categories\r\n  const collapseAllCategories = () => {\r\n    setExpandedNodes(new Set());\r\n  };\r\n\r\n  const getLevelColor = (level: number) => {\r\n    const colors = {\r\n      0: 'text-blue-600 bg-blue-50',\r\n      1: 'text-green-600 bg-green-50', \r\n      2: 'text-orange-600 bg-orange-50',\r\n      3: 'text-purple-600 bg-purple-50'\r\n    };\r\n    return colors[level as keyof typeof colors] || 'text-gray-600 bg-gray-50';\r\n  };\r\n\r\n  const getLevelIcon = (level: number) => {\r\n    const icons = {\r\n      0: '🏷️', // Main Category\r\n      1: '📁', // Category\r\n      2: '📂', // Sub Category\r\n      3: '🗂️'  // Minor Category\r\n    };\r\n    return icons[level as keyof typeof icons] || '📄';\r\n  };\r\n\r\n  const renderCategoryNode = (category: CategoryNode, depth: number = 0) => {\r\n    const isExpanded = expandedNodes.has(category.id);\r\n    const isEditing = editingCategory === category.id;\r\n    const hasChildren = category.children && category.children.length > 0;\r\n    const canAddChild = (category.level || 0) < 3; // Max 4 levels (0-3)\r\n    \r\n    console.log(`Rendering category: ${category.name}, Level: ${category.level}, Can add child: ${canAddChild}`);\r\n\r\n    return (\r\n      <div key={category.id} className=\"mb-1\">\r\n        {/* Category Row */}\r\n        <div \r\n          className={`flex items-center group hover:bg-gray-50 rounded-lg transition-colors ${depth > 0 ? 'ml-6' : ''}`}\r\n          style={{ paddingLeft: `${depth * 12}px` }}\r\n        >\r\n          {/* Expand/Collapse Button */}\r\n          <button\r\n            onClick={() => toggleExpanded(category.id)}\r\n            className={`w-6 h-6 flex items-center justify-center mr-2 rounded hover:bg-gray-200 transition-colors ${\r\n              hasChildren ? 'text-gray-600' : 'text-transparent'\r\n            }`}\r\n            disabled={!hasChildren}\r\n          >\r\n            {hasChildren ? (\r\n              isExpanded ? <ChevronDown size={16} /> : <ChevronRight size={16} />\r\n            ) : null}\r\n          </button>\r\n\r\n          {/* Category Icon */}\r\n          <span className=\"text-lg mr-2\">{getLevelIcon(category.level || 0)}</span>\r\n\r\n          {/* Category Content */}\r\n          <div className=\"flex-1 min-w-0\">\r\n            {isEditing ? (\r\n              <div className=\"flex flex-col gap-2 py-2\">\r\n                <input\r\n                  type=\"text\"\r\n                  value={editingName}\r\n                  onChange={(e) => setEditingName(e.target.value)}\r\n                  className=\"px-3 py-1 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                  placeholder=\"Category name\"\r\n                  autoFocus\r\n                />\r\n                <input\r\n                  type=\"text\"\r\n                  value={editingDescription}\r\n                  onChange={(e) => setEditingDescription(e.target.value)}\r\n                  className=\"px-3 py-1 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                  placeholder=\"Description (optional)\"\r\n                />\r\n                <div className=\"flex gap-2\">\r\n                  <button\r\n                    onClick={saveEdit}\r\n                    className=\"px-3 py-1 bg-green-600 text-white rounded hover:bg-green-700 transition-colors flex items-center gap-1\"\r\n                    disabled={loading}\r\n                  >\r\n                    <Save size={14} />\r\n                    Save\r\n                  </button>\r\n                  <button\r\n                    onClick={cancelEdit}\r\n                    className=\"px-3 py-1 bg-gray-500 text-white rounded hover:bg-gray-600 transition-colors flex items-center gap-1\"\r\n                  >\r\n                    <X size={14} />\r\n                    Cancel\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            ) : (\r\n              <div className=\"py-2\">\r\n                <div className=\"flex items-center gap-2\">\r\n                  <span className=\"font-medium text-gray-900\">{category.name}</span>\r\n                  <span className={`px-2 py-1 text-xs rounded-full ${getLevelColor(category.level || 0)}`}>\r\n                    Level {(category.level || 0) + 1}\r\n                  </span>\r\n                  {category.is_active === false && (\r\n                    <span className=\"px-2 py-1 text-xs bg-red-100 text-red-600 rounded-full\">\r\n                      Inactive\r\n                    </span>\r\n                  )}\r\n                </div>\r\n                {category.description && (\r\n                  <p className=\"text-sm text-gray-600 mt-1\">{category.description}</p>\r\n                )}\r\n                {category.full_path && (\r\n                  <p className=\"text-xs text-gray-500 mt-1\">Path: {category.full_path}</p>\r\n                )}\r\n              </div>\r\n            )}\r\n          </div>\r\n\r\n          {/* Action Buttons */}\r\n          {!isEditing && (\r\n            <div className=\"flex items-center gap-1 opacity-100 transition-opacity\">\r\n              {canAddChild && (\r\n                <button\r\n                  onClick={() => {\r\n                    const currentLevel = category.level || 0;\r\n                    const nextLevel = currentLevel + 1; // Correct calculation: current level + 1\r\n                    console.log(`Adding subcategory to: ${category.name} (Level ${currentLevel}) -> Next level: ${nextLevel}`);\r\n                    startCreate(category.id, nextLevel + 1); // Add 1 to convert to 1-based indexing for typeMap\r\n                  }}\r\n                  className=\"p-1 text-green-600 hover:bg-green-100 rounded transition-colors\"\r\n                  title=\"Add subcategory\"\r\n                >\r\n                  <FolderPlus size={16} />\r\n                </button>\r\n              )}\r\n              <button\r\n                onClick={() => startEdit(category)}\r\n                className=\"p-1 text-blue-600 hover:bg-blue-100 rounded transition-colors\"\r\n                title=\"Edit category\"\r\n              >\r\n                <Edit2 size={16} />\r\n              </button>\r\n              <button\r\n                onClick={() => startDelete(category)}\r\n                className=\"p-1 text-red-600 hover:bg-red-100 rounded transition-colors\"\r\n                title=\"Delete category\"\r\n              >\r\n                <Trash2 size={16} />\r\n              </button>\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Children */}\r\n        {isExpanded && hasChildren && (\r\n          <div className=\"ml-6 border-l border-gray-200 pl-2\">\r\n            {category.children.map(child => renderCategoryNode(child, depth + 1))}\r\n          </div>\r\n        )}\r\n      </div>\r\n    );\r\n  };\r\n\r\n\r\n\r\n  if (!isOpen) return null;\r\n\r\n  return (\r\n    <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4\">\r\n      <div className=\"bg-white rounded-lg shadow-xl w-full max-w-4xl max-h-[90vh] flex flex-col\">\r\n        {/* Header */}\r\n        <div className=\"flex items-center justify-between p-6 border-b border-gray-200\">\r\n          <div className=\"flex items-center gap-3\">\r\n            <Settings className=\"text-blue-600\" size={24} />\r\n            <div>\r\n              <h2 className=\"text-xl font-semibold text-gray-900\">\r\n                Manage {type === 'document' ? 'Document' : 'Website'} Categories\r\n              </h2>\r\n              <p className=\"text-sm text-gray-600\">\r\n                Create, edit, and organize your category hierarchy\r\n              </p>\r\n            </div>\r\n          </div>\r\n          <button\r\n            onClick={onClose}\r\n            className=\"text-gray-400 hover:text-gray-600 transition-colors\"\r\n          >\r\n            <X size={24} />\r\n          </button>\r\n        </div>\r\n\r\n        {/* Stats Bar */}\r\n        {stats && (\r\n          <div className=\"px-6 py-4 bg-gray-50 border-b border-gray-200\">\r\n            <div className=\"grid grid-cols-2 md:grid-cols-6 gap-4 text-sm\">\r\n              <div className=\"text-center\">\r\n                <div className=\"font-semibold text-gray-900\">{stats.statistics.main_categories}</div>\r\n                <div className=\"text-gray-600\">Main Categories</div>\r\n              </div>\r\n              <div className=\"text-center\">\r\n                <div className=\"font-semibold text-gray-900\">{stats.statistics.categories}</div>\r\n                <div className=\"text-gray-600\">Categories</div>\r\n              </div>\r\n              <div className=\"text-center\">\r\n                <div className=\"font-semibold text-gray-900\">{stats.statistics.sub_categories}</div>\r\n                <div className=\"text-gray-600\">Subcategories</div>\r\n              </div>\r\n              <div className=\"text-center\">\r\n                <div className=\"font-semibold text-gray-900\">{stats.statistics.minor_categories}</div>\r\n                <div className=\"text-gray-600\">Minor Categories</div>\r\n              </div>\r\n              <div className=\"text-center\">\r\n                <div className=\"font-semibold text-green-600\">{stats.statistics.active}</div>\r\n                <div className=\"text-gray-600\">Active</div>\r\n              </div>\r\n              <div className=\"text-center\">\r\n                <div className=\"font-semibold text-red-600\">{stats.statistics.inactive}</div>\r\n                <div className=\"text-gray-600\">Inactive</div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Messages */}\r\n        {error && (\r\n          <div className=\"mx-6 mt-4 p-3 bg-red-50 border border-red-200 rounded-md\">\r\n            <div className=\"flex items-center gap-2 text-red-700\">\r\n              <AlertTriangle size={16} />\r\n              <span className=\"text-sm\">{error}</span>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {success && (\r\n          <div className=\"mx-6 mt-4 p-3 bg-green-50 border border-green-200 rounded-md\">\r\n            <div className=\"flex items-center gap-2 text-green-700\">\r\n              <Check size={16} />\r\n              <span className=\"text-sm\">{success}</span>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Action Bar */}\r\n        <div className=\"px-6 py-4 border-b border-gray-200\">\r\n          <div className=\"flex items-center justify-between\">\r\n            <div className=\"flex items-center gap-3\">\r\n              <button\r\n                onClick={() => startCreate(null, 1)}\r\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors flex items-center gap-2\"\r\n                disabled={loading}\r\n              >\r\n                <Plus size={16} />\r\n                Add Main Category\r\n              </button>\r\n              <button\r\n                onClick={() => setShowBulkImport(true)}\r\n                className=\"px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 transition-colors flex items-center gap-2\"\r\n                disabled={loading}\r\n              >\r\n                <Upload size={16} />\r\n                Bulk Import\r\n              </button>\r\n              <button\r\n                onClick={loadCategories}\r\n                className=\"px-4 py-2 bg-gray-100 text-gray-700 rounded-lg hover:bg-gray-200 transition-colors\"\r\n                disabled={loading}\r\n              >\r\n                Refresh\r\n              </button>\r\n              {/* Expand/Collapse All Controls */}\r\n              <div className=\"flex items-center gap-2 border-l border-gray-300 pl-3\">\r\n                <button\r\n                  onClick={expandAllCategories}\r\n                  className=\"px-3 py-1 text-sm bg-blue-50 text-blue-600 rounded hover:bg-blue-100 transition-colors flex items-center gap-1\"\r\n                  disabled={loading || categories.length === 0}\r\n                  title=\"Expand all categories\"\r\n                >\r\n                  <ChevronDown size={14} />\r\n                  Expand All\r\n                </button>\r\n                <button\r\n                  onClick={collapseAllCategories}\r\n                  className=\"px-3 py-1 text-sm bg-gray-50 text-gray-600 rounded hover:bg-gray-100 transition-colors flex items-center gap-1\"\r\n                  disabled={loading || categories.length === 0}\r\n                  title=\"Collapse all categories\"\r\n                >\r\n                  <ChevronRight size={14} />\r\n                  Collapse All\r\n                </button>\r\n              </div>\r\n            </div>\r\n            <div className=\"text-sm text-gray-600\">\r\n              Total: {stats?.total_count || 0} categories\r\n            </div>\r\n          </div>\r\n        </div>\r\n\r\n        {/* Content */}\r\n        <div className=\"flex-1 overflow-auto p-6\">\r\n          {loading && !categories.length ? (\r\n            <div className=\"flex items-center justify-center py-12\">\r\n              <div className=\"animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600\"></div>\r\n              <span className=\"ml-3 text-gray-600\">Loading categories...</span>\r\n            </div>\r\n          ) : categories.length === 0 ? (\r\n            <div className=\"text-center py-12\">\r\n              <div className=\"text-gray-400 text-lg mb-2\">📁</div>\r\n              <h3 className=\"text-lg font-medium text-gray-900 mb-2\">No Categories Found</h3>\r\n              <p className=\"text-gray-600 mb-4\">\r\n                Get started by creating your first main category.\r\n              </p>\r\n              <button\r\n                onClick={() => startCreate(null, 1)}\r\n                className=\"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors\"\r\n              >\r\n                Create First Category\r\n              </button>\r\n            </div>\r\n          ) : (\r\n            <div className=\"space-y-1\">\r\n              {categories.map(category => renderCategoryNode(category))}\r\n            </div>\r\n          )}\r\n        </div>\r\n\r\n        {/* Create Form Modal */}\r\n        {showCreateForm && (\r\n          <div className=\"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4\">\r\n            <div className=\"bg-white rounded-lg shadow-xl w-full max-w-md\">\r\n              <div className=\"p-6\">\r\n                <h3 className=\"text-lg font-semibold text-gray-900 mb-4\">\r\n                  Create New Category (Level {createLevel})\r\n                  {createParentId && <span className=\"text-sm text-gray-600\"> - Parent ID: {createParentId}</span>}\r\n                </h3>\r\n                <div className=\"space-y-4\">\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                      Category Name *\r\n                    </label>\r\n                    <input\r\n                      type=\"text\"\r\n                      value={newCategoryName}\r\n                      onChange={(e) => setNewCategoryName(e.target.value)}\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                      placeholder=\"Enter category name\"\r\n                      autoFocus\r\n                    />\r\n                  </div>\r\n                  <div>\r\n                    <label className=\"block text-sm font-medium text-gray-700 mb-1\">\r\n                      Description\r\n                    </label>\r\n                    <textarea\r\n                      value={newCategoryDescription}\r\n                      onChange={(e) => setNewCategoryDescription(e.target.value)}\r\n                      className=\"w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500\"\r\n                      placeholder=\"Optional description\"\r\n                      rows={3}\r\n                    />\r\n                  </div>\r\n                  <div className=\"flex gap-3 justify-end\">\r\n                    <button\r\n                      onClick={cancelCreate}\r\n                      className=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors\"\r\n                    >\r\n                      Cancel\r\n                    </button>\r\n                    <button\r\n                      onClick={createNewCategory}\r\n                      className=\"px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors\"\r\n                      disabled={!newCategoryName.trim() || loading}\r\n                    >\r\n                      Create Category\r\n                    </button>\r\n                  </div>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Delete Confirmation Modal */}\r\n        {deleteConfirmation.isOpen && deleteConfirmation.category && (\r\n          <div className=\"absolute inset-0 bg-black bg-opacity-50 flex items-center justify-center p-4\">\r\n            <div className=\"bg-white rounded-lg shadow-xl w-full max-w-md\">\r\n              <div className=\"p-6\">\r\n                <div className=\"flex items-center gap-3 mb-4\">\r\n                  <AlertTriangle className=\"text-red-600\" size={24} />\r\n                  <h3 className=\"text-lg font-semibold text-gray-900\">\r\n                    Delete Category\r\n                  </h3>\r\n                </div>\r\n                <div className=\"space-y-3 mb-6\">\r\n                  <p className=\"text-gray-700\">\r\n                    Are you sure you want to delete <strong>\"{deleteConfirmation.category.name}\"</strong>?\r\n                  </p>\r\n                  {deleteConfirmation.hasChildren && (\r\n                    <p className=\"text-red-600 text-sm\">\r\n                      ⚠️ This category has subcategories that will also be deleted.\r\n                    </p>\r\n                  )}\r\n                  {deleteConfirmation.linkedCount > 0 && (\r\n                    <p className=\"text-red-600 text-sm\">\r\n                      ⚠️ This category is linked to {deleteConfirmation.linkedCount} {type}(s).\r\n                    </p>\r\n                  )}\r\n                  <p className=\"text-gray-600 text-sm\">\r\n                    This action cannot be undone.\r\n                  </p>\r\n                </div>\r\n                <div className=\"flex gap-3 justify-end\">\r\n                  <button\r\n                    onClick={cancelDelete}\r\n                    className=\"px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors\"\r\n                  >\r\n                    Cancel\r\n                  </button>\r\n                  <button\r\n                    onClick={confirmDelete}\r\n                    className=\"px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors\"\r\n                    disabled={loading}\r\n                  >\r\n                    Delete Category\r\n                  </button>\r\n                </div>\r\n              </div>\r\n            </div>\r\n          </div>\r\n        )}\r\n\r\n        {/* Bulk Import Modal */}\r\n        <BulkCategoryImport\r\n          isOpen={showBulkImport}\r\n          onClose={() => setShowBulkImport(false)}\r\n          type={type}\r\n          onImportComplete={handleBulkImportComplete}\r\n        />\r\n      </div>\r\n    </div>\r\n  );\r\n};\r\n\r\nexport default ManageCategoriesModal; ", "C:\\IR App\\frontend\\src\\components\\categories\\BulkCategoryImport.tsx", [], "C:\\IR App\\frontend\\src\\components\\ui\\CategorySelectorModal.tsx", [], "C:\\IR App\\frontend\\src\\utils\\supabaseClient.ts", ["154"], "import { createClient } from '@supabase/supabase-js';\r\n\r\n// Get environment variables with fallbacks\r\nconst supabaseUrl = process.env.REACT_APP_SUPABASE_URL || 'https://rkllidjktazafeinezgo.supabase.co';\r\nconst supabaseAnonKey = process.env.REACT_APP_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.nWa8cQuaiZB6oGmjsOIuZT7rmAni2KlHoHbJ8D0QSpA';\r\n\r\n// Create and export the Supabase client\r\nconst supabaseClient = createClient(supabaseUrl, supabaseAnonKey);\r\n\r\n// Add connection health check\r\nexport const checkSupabaseConnection = async () => {\r\n  try {\r\n    // Try to fetch a single row from a known table\r\n    const { data, error } = await supabaseClient\r\n      .from('documents')\r\n      .select('id')\r\n      .limit(1);\r\n\r\n    if (error) {\r\n      console.error('Supabase connection error:', error.message);\r\n      return false;\r\n    }\r\n\r\n    console.log('Supabase connection successful');\r\n    return true;\r\n  } catch (err) {\r\n    console.error('Supabase connection check failed:', err);\r\n    return false;\r\n  }\r\n};\r\n\r\nexport default supabaseClient;\r\n", {"ruleId": "155", "replacedBy": "156"}, {"ruleId": "157", "replacedBy": "158"}, {"ruleId": "159", "severity": 1, "message": "160", "line": 393, "column": 19, "nodeType": "161", "messageId": "162", "endLine": 393, "endColumn": 29}, {"ruleId": "159", "severity": 1, "message": "163", "line": 17, "column": 10, "nodeType": "161", "messageId": "162", "endLine": 17, "endColumn": 20}, {"ruleId": "159", "severity": 1, "message": "164", "line": 18, "column": 10, "nodeType": "161", "messageId": "162", "endLine": 18, "endColumn": 20}, {"ruleId": "159", "severity": 1, "message": "165", "line": 20, "column": 10, "nodeType": "161", "messageId": "162", "endLine": 20, "endColumn": 22}, {"ruleId": "159", "severity": 1, "message": "166", "line": 20, "column": 24, "nodeType": "161", "messageId": "162", "endLine": 20, "endColumn": 39}, {"ruleId": "167", "severity": 1, "message": "168", "line": 37, "column": 6, "nodeType": "169", "endLine": 37, "endColumn": 56, "suggestions": "170"}, {"ruleId": "159", "severity": 1, "message": "171", "line": 143, "column": 9, "nodeType": "161", "messageId": "162", "endLine": 143, "endColumn": 24}, {"ruleId": "172", "severity": 1, "message": "173", "line": 210, "column": 15, "nodeType": "174", "endLine": 215, "endColumn": 17}, {"ruleId": "172", "severity": 1, "message": "173", "line": 232, "column": 11, "nodeType": "174", "endLine": 242, "endColumn": 13}, {"ruleId": "175", "severity": 1, "message": "176", "line": 433, "column": 39, "nodeType": "177", "messageId": "178", "endLine": 436, "endColumn": 12}, {"ruleId": "175", "severity": 1, "message": "176", "line": 479, "column": 27, "nodeType": "177", "messageId": "178", "endLine": 483, "endColumn": 8}, {"ruleId": "159", "severity": 1, "message": "179", "line": 1, "column": 38, "nodeType": "161", "messageId": "162", "endLine": 1, "endColumn": 49}, {"ruleId": "159", "severity": 1, "message": "180", "line": 12, "column": 3, "nodeType": "161", "messageId": "162", "endLine": 12, "endColumn": 23}, {"ruleId": "167", "severity": 1, "message": "181", "line": 91, "column": 6, "nodeType": "169", "endLine": 91, "endColumn": 20, "suggestions": "182"}, {"ruleId": "159", "severity": 1, "message": "183", "line": 14, "column": 13, "nodeType": "161", "messageId": "162", "endLine": 14, "endColumn": 17}, "no-native-reassign", ["184"], "no-negated-in-lhs", ["185"], "@typescript-eslint/no-unused-vars", "'tableCheck' is assigned a value but never used.", "Identifier", "unusedVar", "'imageError' is assigned a value but never used.", "'imageTries' is assigned a value but never used.", "'visibleDebug' is assigned a value but never used.", "'setVisibleDebug' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'source.content_type' and 'source.visual_content'. Either include them or remove the dependency array.", "ArrayExpression", ["186"], "'getImageSources' is assigned a value but never used.", "jsx-a11y/img-redundant-alt", "Redundant alt attribute. Screen-readers already announce `img` tags as an image. You don’t need to use the words `image`, `photo,` or `picture` (or any specified custom words) in the alt prop.", "JSXOpeningElement", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'html'.", "ArrowFunctionExpression", "unsafeRefs", "'useCallback' is defined but never used.", "'getCategoryHierarchy' is defined but never used.", "React Hook useEffect has a missing dependency: 'loadCategories'. Either include it or remove the dependency array.", ["187"], "'data' is assigned a value but never used.", "no-global-assign", "no-unsafe-negation", {"desc": "188", "fix": "189"}, {"desc": "190", "fix": "191"}, "Update the dependencies array to be: [source.content_type, source.filename, source.page, source.storage_url, source.visual_content]", {"range": "192", "text": "193"}, "Update the dependencies array to be: [isOpen, loadCategories, type]", {"range": "194", "text": "195"}, [1319, 1369], "[source.content_type, source.filename, source.page, source.storage_url, source.visual_content]", [2880, 2894], "[isOpen, loadCategories, type]"]