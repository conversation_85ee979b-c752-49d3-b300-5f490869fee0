#!/usr/bin/env python3
"""
RailGPT Strict 3-Tier Priority System Test Suite

Tests the four validation scenarios as specified:
1. FSDS full form (documents only)
2. Rapid response app (websites only) 
3. VASP development (both sources)
4. WDG4G details (LLM fallback)

Validates proper source prioritization and answer generation.
"""

import requests
import json
import time
from typing import Dict, Any

# Configuration
BASE_URL = "http://localhost:8000"
QUERY_ENDPOINT = f"{BASE_URL}/api/query"

# Test queries for validation scenarios
TEST_QUERIES = {
    "documents_only": {
        "query": "FSDS full form",
        "description": "Should return document-only answer",
        "expected_priority": "document",
        "expected_llm_fallback": False
    },
    "websites_only": {
        "query": "Rapid response app",
        "description": "Should return website-only answer",
        "expected_priority": "website", 
        "expected_llm_fallback": False
    },
    "both_sources": {
        "query": "VASP development",
        "description": "Should return highest priority source (document if available)",
        "expected_priority": "document_or_website",
        "expected_llm_fallback": False
    },
    "llm_fallback": {
        "query": "WDG4G details",
        "description": "Should trigger LLM fallback (no relevant chunks)",
        "expected_priority": "llm_fallback",
        "expected_llm_fallback": True
    }
}

def send_test_query(query: str, model: str = "qwen") -> Dict[str, Any]:
    """Send a test query to the RailGPT API."""
    payload = {
        "query": query,
        "model": model,
        "fallback_enabled": True,
        "extract_format": "paragraph",
        "use_hybrid_search": True
    }
    
    try:
        response = requests.post(QUERY_ENDPOINT, json=payload, timeout=30)
        response.raise_for_status()
        return response.json()
    except requests.exceptions.RequestException as e:
        print(f"❌ Request failed: {e}")
        return {"error": str(e)}

def analyze_response(response: Dict[str, Any], test_case: str) -> Dict[str, Any]:
    """Analyze the response to determine which priority tier was used."""
    analysis = {
        "test_case": test_case,
        "has_document_answer": bool(response.get("document_answer", "").strip()),
        "has_website_answer": bool(response.get("website_answer", "").strip()),
        "llm_fallback_used": response.get("llm_fallback_used", False),
        "document_sources_count": len(response.get("document_sources", [])),
        "website_sources_count": len(response.get("website_sources", [])),
        "total_sources_count": len(response.get("sources", [])),
        "answer_length": len(response.get("answer", "")),
        "model_used": response.get("llm_model", "unknown")
    }
    
    # Determine which tier was actually used
    if analysis["has_document_answer"]:
        analysis["actual_priority"] = "document"
        analysis["tier_used"] = "Tier 1 (Document - Highest Priority)"
    elif analysis["has_website_answer"]:
        analysis["actual_priority"] = "website"
        analysis["tier_used"] = "Tier 2 (Website - Medium Priority)"
    elif analysis["llm_fallback_used"]:
        analysis["actual_priority"] = "llm_fallback"
        analysis["tier_used"] = "Tier 3 (LLM Fallback - Lowest Priority)"
    else:
        analysis["actual_priority"] = "unknown"
        analysis["tier_used"] = "Unknown/Error"
    
    return analysis

def validate_strict_priority(analysis: Dict[str, Any]) -> bool:
    """Validate that strict priority rules are followed."""
    # Rule 1: If document answer exists, website answer should be None (strict priority)
    if analysis["has_document_answer"] and analysis["has_website_answer"]:
        print(f"⚠️  PRIORITY VIOLATION: Both document and website answers present (should be strict priority)")
        return False
    
    # Rule 2: If LLM fallback is used, no chunk sources should be present
    if analysis["llm_fallback_used"] and (analysis["document_sources_count"] > 0 or analysis["website_sources_count"] > 0):
        print(f"⚠️  FALLBACK VIOLATION: LLM fallback used but sources present")
        return False
    
    # Rule 3: If sources are present, LLM fallback should not be used
    if (analysis["document_sources_count"] > 0 or analysis["website_sources_count"] > 0) and analysis["llm_fallback_used"]:
        print(f"⚠️  SOURCE VIOLATION: Sources present but LLM fallback used")
        return False
    
    return True

def run_comprehensive_test():
    """Run the comprehensive RailGPT priority system test."""
    print("🚀 Starting RailGPT Strict 3-Tier Priority System Test Suite")
    print("=" * 70)
    
    results = {}
    all_passed = True
    
    for test_id, test_config in TEST_QUERIES.items():
        print(f"\n📋 Test Case: {test_id.upper()}")
        print(f"Query: '{test_config['query']}'")
        print(f"Description: {test_config['description']}")
        print(f"Expected: {test_config['expected_priority']}")
        
        # Send the query
        print("🔍 Sending query...")
        start_time = time.time()
        response = send_test_query(test_config["query"])
        end_time = time.time()
        
        if "error" in response:
            print(f"❌ Test failed: {response['error']}")
            results[test_id] = {"status": "failed", "error": response["error"]}
            all_passed = False
            continue
        
        # Analyze the response
        analysis = analyze_response(response, test_id)
        processing_time = end_time - start_time
        
        print(f"⏱️  Processing time: {processing_time:.2f} seconds")
        print(f"🎯 Actual priority: {analysis['actual_priority']}")
        print(f"🏷️  Tier used: {analysis['tier_used']}")
        print(f"📊 Sources: Doc={analysis['document_sources_count']}, Web={analysis['website_sources_count']}")
        print(f"🤖 LLM fallback: {analysis['llm_fallback_used']}")
        print(f"📝 Answer length: {analysis['answer_length']} characters")
        
        # Validate strict priority rules
        priority_valid = validate_strict_priority(analysis)
        
        # Check if result matches expectation
        expected = test_config["expected_priority"]
        actual = analysis["actual_priority"]
        
        if expected == "document_or_website":
            expectation_met = actual in ["document", "website"]
        else:
            expectation_met = actual == expected
        
        # Check LLM fallback expectation
        fallback_correct = analysis["llm_fallback_used"] == test_config["expected_llm_fallback"]
        
        # Overall test result
        test_passed = priority_valid and expectation_met and fallback_correct and processing_time < 10
        
        if test_passed:
            print("✅ Test PASSED")
        else:
            print("❌ Test FAILED")
            if not priority_valid:
                print("   - Priority rules violated")
            if not expectation_met:
                print(f"   - Expected {expected}, got {actual}")
            if not fallback_correct:
                print(f"   - LLM fallback expectation not met")
            if processing_time >= 10:
                print(f"   - Processing time too slow: {processing_time:.2f}s")
            all_passed = False
        
        results[test_id] = {
            "status": "passed" if test_passed else "failed",
            "analysis": analysis,
            "processing_time": processing_time,
            "priority_valid": priority_valid,
            "expectation_met": expectation_met,
            "fallback_correct": fallback_correct
        }
    
    # Summary
    print("\n" + "=" * 70)
    print("📋 TEST SUMMARY")
    print("=" * 70)
    
    passed_count = sum(1 for r in results.values() if r["status"] == "passed")
    total_count = len(results)
    
    print(f"Tests passed: {passed_count}/{total_count}")
    
    if all_passed:
        print("🎉 ALL TESTS PASSED - RailGPT Strict 3-Tier Priority System is working correctly!")
    else:
        print("⚠️  SOME TESTS FAILED - Review the implementation")
    
    # Detailed results
    print("\n📊 Detailed Results:")
    for test_id, result in results.items():
        status_icon = "✅" if result["status"] == "passed" else "❌"
        print(f"{status_icon} {test_id}: {result['status']}")
        if result["status"] == "passed" and "analysis" in result:
            analysis = result["analysis"]
            print(f"   Priority: {analysis['actual_priority']} | Sources: {analysis['total_sources_count']} | Time: {result['processing_time']:.2f}s")
    
    return all_passed, results

if __name__ == "__main__":
    success, test_results = run_comprehensive_test()
    exit(0 if success else 1)
