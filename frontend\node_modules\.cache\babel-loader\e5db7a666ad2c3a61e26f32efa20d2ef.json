{"ast": null, "code": "// Supabase client for frontend\nimport supabaseClient, { checkSupabaseConnection } from '../utils/supabaseClient';\n\n// Export the client for use throughout the application\nexport const supabase = supabaseClient;\n\n// Export the connection check function\nexport const testSupabaseConnection = checkSupabaseConnection;\n\n// Document types\n\n// Website types\n\n// Query types\n\n// Document operations\nexport const getDocuments = async () => {\n  try {\n    const {\n      data,\n      error\n    } = await supabase.from('documents').select('*').order('created_at', {\n      ascending: false\n    });\n    if (error) {\n      console.error('Error fetching documents:', error);\n      return [];\n    }\n    return data || [];\n  } catch (error) {\n    console.error('Error in getDocuments:', error);\n    return [];\n  }\n};\nexport const getDocumentById = async id => {\n  try {\n    const {\n      data,\n      error\n    } = await supabase.from('documents').select('*').eq('id', id).single();\n    if (error) {\n      console.error('Error fetching document:', error);\n      return null;\n    }\n    return data;\n  } catch (error) {\n    console.error('Error in getDocumentById:', error);\n    return null;\n  }\n};\nexport const getDocumentChunks = async documentId => {\n  try {\n    const {\n      data,\n      error\n    } = await supabase.from('document_chunks').select('*').eq('document_id', documentId).order('chunk_index', {\n      ascending: true\n    });\n    if (error) {\n      console.error('Error fetching document chunks:', error);\n      return [];\n    }\n    return data || [];\n  } catch (error) {\n    console.error('Error in getDocumentChunks:', error);\n    return [];\n  }\n};\n\n// Website operations\nexport const getWebsites = async () => {\n  try {\n    const {\n      data,\n      error\n    } = await supabase.from('websites').select('*').order('created_at', {\n      ascending: false\n    });\n    if (error) {\n      console.error('Error fetching websites:', error);\n      return [];\n    }\n    return data || [];\n  } catch (error) {\n    console.error('Error in getWebsites:', error);\n    return [];\n  }\n};\nexport const getWebsiteById = async id => {\n  try {\n    const {\n      data,\n      error\n    } = await supabase.from('websites').select('*').eq('id', id).single();\n    if (error) {\n      console.error('Error fetching website:', error);\n      return null;\n    }\n    return data;\n  } catch (error) {\n    console.error('Error in getWebsiteById:', error);\n    return null;\n  }\n};\n\n// Query operations\nexport const saveQuery = async query => {\n  try {\n    const {\n      data,\n      error\n    } = await supabase.from('queries').insert([query]).select().single();\n    if (error) {\n      console.error('Error saving query:', error);\n      return null;\n    }\n    return data;\n  } catch (error) {\n    console.error('Error in saveQuery:', error);\n    return null;\n  }\n};\nexport const getRecentQueries = async (limit = 10) => {\n  try {\n    const {\n      data,\n      error\n    } = await supabase.from('queries').select('*').order('created_at', {\n      ascending: false\n    }).limit(limit);\n    if (error) {\n      console.error('Error fetching recent queries:', error);\n      return [];\n    }\n    return data || [];\n  } catch (error) {\n    console.error('Error in getRecentQueries:', error);\n    return [];\n  }\n};\n\n// Storage operations\nexport const uploadFile = async (filePath, file, onProgress) => {\n  try {\n    console.log(`🔄 Starting file upload: ${filePath}`);\n    console.log(`📁 File details: ${file.name}, ${file.size} bytes, ${file.type}`);\n\n    // Try multiple upload strategies to handle RLS issues\n\n    // Strategy 1: Direct upload with auth context\n    try {\n      const {\n        data,\n        error\n      } = await supabase.storage.from('documents').upload(filePath, file, {\n        cacheControl: '3600',\n        upsert: true,\n        // Allow overwriting\n        contentType: file.type || 'application/octet-stream'\n      });\n      if (error) {\n        console.warn(`⚠️ Strategy 1 failed:`, error);\n        throw error;\n      }\n\n      // Get public URL\n      const {\n        data: {\n          publicUrl\n        }\n      } = supabase.storage.from('documents').getPublicUrl(data.path);\n      console.log(`✅ Strategy 1 success: ${publicUrl}`);\n      return publicUrl;\n    } catch (strategy1Error) {\n      console.warn(`Strategy 1 failed, trying Strategy 2:`, strategy1Error);\n\n      // Strategy 2: Upload with simplified path and anonymous context\n      try {\n        // Create simpler file path\n        const simplePath = `${Date.now()}_${file.name}`;\n        const {\n          data,\n          error\n        } = await supabase.storage.from('documents').upload(simplePath, file, {\n          upsert: true,\n          contentType: file.type\n        });\n        if (error) {\n          console.warn(`⚠️ Strategy 2 failed:`, error);\n          throw error;\n        }\n        const {\n          data: {\n            publicUrl\n          }\n        } = supabase.storage.from('documents').getPublicUrl(data.path);\n        console.log(`✅ Strategy 2 success: ${publicUrl}`);\n        return publicUrl;\n      } catch (strategy2Error) {\n        console.warn(`Strategy 2 failed, trying Strategy 3:`, strategy2Error);\n\n        // Strategy 3: Use backend upload endpoint as fallback\n        try {\n          const formData = new FormData();\n          formData.append('file', file);\n          formData.append('uploaded_by', 'frontend_user');\n          const response = await fetch('/api/upload-document', {\n            method: 'POST',\n            body: formData\n          });\n          if (!response.ok) {\n            throw new Error(`Backend upload failed: ${response.statusText}`);\n          }\n          const result = await response.json();\n          console.log(`✅ Strategy 3 success via backend:`, result);\n          return result.storage_url || result.local_path || 'uploaded_via_backend';\n        } catch (strategy3Error) {\n          console.error(`❌ All upload strategies failed:`, {\n            strategy1: strategy1Error,\n            strategy2: strategy2Error,\n            strategy3: strategy3Error\n          });\n\n          // Return null to indicate failure\n          return null;\n        }\n      }\n    }\n  } catch (error) {\n    console.error('❌ Critical upload error:', error);\n    return null;\n  }\n};\nexport const getFileUrl = filePath => {\n  const {\n    data: {\n      publicUrl\n    }\n  } = supabase.storage.from('documents').getPublicUrl(filePath);\n  return publicUrl;\n};\n\n// Chat Session types\n\n// Chat Session operations\nexport const createChatSession = async (title = 'New Chat', modelUsed = 'gemini-2.0-flash') => {\n  try {\n    const {\n      data,\n      error\n    } = await supabase.from('chat_sessions').insert([{\n      title,\n      messages: [],\n      model_used: modelUsed,\n      has_document: false,\n      has_website: false\n    }]).select().single();\n    if (error) {\n      console.error('Error creating chat session:', error);\n      return null;\n    }\n    return {\n      ...data,\n      messages: data.messages || []\n    };\n  } catch (error) {\n    console.error('Error in createChatSession:', error);\n    return null;\n  }\n};\nexport const getChatSessions = async userId => {\n  try {\n    console.log('Attempting to fetch chat sessions...');\n\n    // First, try to check if the table exists with a simple query\n    const {\n      data: tableCheck,\n      error: tableError\n    } = await supabase.from('chat_sessions').select('id').limit(1);\n    if (tableError) {\n      console.warn('Chat sessions table not accessible:', tableError.message);\n      console.log('Returning empty chat sessions array');\n      return [];\n    }\n\n    // If table exists, try the full query with basic columns only\n    let query = supabase.from('chat_sessions').select('id, title, created_at, updated_at').order('updated_at', {\n      ascending: false\n    }).limit(10); // Reduced limit\n\n    if (userId) {\n      query = query.eq('user_id', userId);\n    }\n    const {\n      data,\n      error\n    } = await query;\n    if (error) {\n      console.warn('Chat sessions query failed:', error.message);\n      return [];\n    }\n\n    // Process and return data with safe defaults\n    const sessions = (data || []).map(session => ({\n      id: session.id,\n      title: session.title || 'Untitled Chat',\n      created_at: session.created_at,\n      updated_at: session.updated_at,\n      model_used: 'gemini-2.0-flash',\n      has_document: false,\n      has_website: false,\n      messages: []\n    }));\n    console.log(`Successfully loaded ${sessions.length} chat sessions`);\n    return sessions;\n  } catch (error) {\n    console.warn('Error in getChatSessions:', error.message || error);\n    return [];\n  }\n};\nexport const getChatSessionById = async id => {\n  try {\n    const {\n      data,\n      error\n    } = await supabase.from('chat_sessions').select('*').eq('id', id).single();\n    if (error) {\n      console.error('Error fetching chat session:', error);\n      return null;\n    }\n    return {\n      ...data,\n      messages: data.messages || []\n    };\n  } catch (error) {\n    console.error('Error in getChatSessionById:', error);\n    return null;\n  }\n};\nexport const updateChatSession = async (id, updates) => {\n  try {\n    const {\n      data,\n      error\n    } = await supabase.from('chat_sessions').update(updates).eq('id', id).select().single();\n    if (error) {\n      console.error('Error updating chat session:', error);\n      return null;\n    }\n    return {\n      ...data,\n      messages: data.messages || []\n    };\n  } catch (error) {\n    console.error('Error in updateChatSession:', error);\n    return null;\n  }\n};\nexport const updateChatTitle = async (id, title) => {\n  try {\n    const {\n      error\n    } = await supabase.from('chat_sessions').update({\n      title\n    }).eq('id', id);\n    if (error) {\n      console.error('Error updating chat title:', error);\n      return false;\n    }\n    return true;\n  } catch (error) {\n    console.error('Error in updateChatTitle:', error);\n    return false;\n  }\n};\nexport const deleteChatSession = async id => {\n  try {\n    const {\n      error\n    } = await supabase.from('chat_sessions').delete().eq('id', id);\n    if (error) {\n      console.error('Error deleting chat session:', error);\n      return false;\n    }\n    return true;\n  } catch (error) {\n    console.error('Error in deleteChatSession:', error);\n    return false;\n  }\n};\nexport const saveChatMessages = async (chatId, messages) => {\n  try {\n    // Ensure all message fields are preserved when saving to Supabase\n    const sanitizedMessages = messages.map(msg => ({\n      id: msg.id,\n      content: msg.content,\n      document_answer: msg.document_answer || undefined,\n      website_answer: msg.website_answer || undefined,\n      llm_model: msg.llm_model,\n      sender: msg.sender,\n      loading: msg.loading,\n      sources: msg.sources || undefined,\n      document_sources: msg.document_sources || undefined,\n      website_sources: msg.website_sources || undefined,\n      timestamp: msg.timestamp,\n      chatId: msg.chatId,\n      llmFallbackUsed: msg.llmFallbackUsed\n    }));\n\n    // Check if chat has documents or websites in the messages\n    const hasDocument = sanitizedMessages.some(msg => msg.document_answer || msg.document_sources && msg.document_sources.length > 0 || msg.sources && msg.sources.some(s => s.source_type === 'document'));\n    const hasWebsite = sanitizedMessages.some(msg => msg.website_answer || msg.website_sources && msg.website_sources.length > 0 || msg.sources && msg.sources.some(s => s.source_type === 'website'));\n    console.log('Saving chat messages:', {\n      messageCount: sanitizedMessages.length,\n      hasDocument,\n      hasWebsite,\n      messagesWithDocAnswer: sanitizedMessages.filter(m => m.document_answer).length,\n      messagesWithWebAnswer: sanitizedMessages.filter(m => m.website_answer).length\n    });\n    const {\n      error\n    } = await supabase.from('chat_sessions').update({\n      messages: sanitizedMessages,\n      has_document: hasDocument,\n      has_website: hasWebsite\n    }).eq('id', chatId);\n    if (error) {\n      console.error('Error saving chat messages:', error);\n      return false;\n    }\n    return true;\n  } catch (error) {\n    console.error('Error in saveChatMessages:', error);\n    return false;\n  }\n};\nexport const clearAllChatSessions = async userId => {\n  try {\n    let query = supabase.from('chat_sessions').delete();\n    if (userId) {\n      query = query.eq('user_id', userId);\n    }\n    const {\n      error\n    } = await query;\n    if (error) {\n      console.error('Error clearing chat sessions:', error);\n      return false;\n    }\n    return true;\n  } catch (error) {\n    console.error('Error in clearAllChatSessions:', error);\n    return false;\n  }\n};\nexport default supabase;", "map": {"version": 3, "names": ["supabaseClient", "checkSupabaseConnection", "supabase", "testSupabaseConnection", "getDocuments", "data", "error", "from", "select", "order", "ascending", "console", "getDocumentById", "id", "eq", "single", "getDocumentChunks", "documentId", "getWebsites", "getWebsiteById", "saveQuery", "query", "insert", "getRecentQueries", "limit", "uploadFile", "filePath", "file", "onProgress", "log", "name", "size", "type", "storage", "upload", "cacheControl", "upsert", "contentType", "warn", "publicUrl", "getPublicUrl", "path", "strategy1Error", "simplePath", "Date", "now", "strategy2Error", "formData", "FormData", "append", "response", "fetch", "method", "body", "ok", "Error", "statusText", "result", "json", "storage_url", "local_path", "strategy3Error", "strategy1", "strategy2", "strategy3", "getFileUrl", "createChatSession", "title", "modelUsed", "messages", "model_used", "has_document", "has_website", "getChatSessions", "userId", "tableCheck", "tableError", "message", "sessions", "map", "session", "created_at", "updated_at", "length", "getChatSessionById", "updateChatSession", "updates", "update", "updateChatTitle", "deleteChatSession", "delete", "saveChatMessages", "chatId", "sanitizedMessages", "msg", "content", "document_answer", "undefined", "website_answer", "llm_model", "sender", "loading", "sources", "document_sources", "website_sources", "timestamp", "llmFallbackUsed", "hasDocument", "some", "s", "source_type", "hasWebsite", "messageCount", "messagesWithDocAnswer", "filter", "m", "messagesWithWebAnswer", "clearAllChatSessions"], "sources": ["C:/IR App/frontend/src/services/supabase.ts"], "sourcesContent": ["// Supabase client for frontend\nimport supabaseClient, { checkSupabaseConnection } from '../utils/supabaseClient';\n\n// Export the client for use throughout the application\nexport const supabase = supabaseClient;\n\n// Export the connection check function\nexport const testSupabaseConnection = checkSupabaseConnection;\n\n// Document types\nexport interface Document {\n  id: string;\n  filename: string;\n  display_name?: string;\n  file_path: string;\n  file_type?: string;\n  file_size?: number;\n  main_category?: string;\n  category?: string;\n  sub_category?: string;\n  minor_category?: string;\n  uploaded_by?: string;\n  created_at?: string;\n  updated_at?: string;\n  status?: string;\n  quality_score?: number;\n}\n\nexport interface DocumentChunk {\n  id: string;\n  document_id: string;\n  chunk_index: number;\n  page_number: number;\n  text: string;\n  metadata?: any;\n  created_at?: string;\n}\n\n// Website types\nexport interface Website {\n  id: string;\n  url: string;\n  domain?: string;\n  title?: string;\n  description?: string;\n  category?: string;\n  submitted_by?: string;\n  created_at?: string;\n  updated_at?: string;\n  status?: string;\n  quality_score?: number;\n}\n\nexport interface WebsiteChunk {\n  id: string;\n  website_id: string;\n  chunk_index: number;\n  text: string;\n  metadata?: any;\n  created_at?: string;\n}\n\n// Query types\nexport interface Query {\n  id: string;\n  user_id?: string;\n  query_text: string;\n  answer_text?: string;\n  llm_model?: string;\n  sources?: any;\n  created_at?: string;\n  processing_time?: number;\n}\n\n// Document operations\nexport const getDocuments = async (): Promise<Document[]> => {\n  try {\n    const { data, error } = await supabase\n      .from('documents')\n      .select('*')\n      .order('created_at', { ascending: false });\n\n    if (error) {\n      console.error('Error fetching documents:', error);\n      return [];\n    }\n\n    return data || [];\n  } catch (error) {\n    console.error('Error in getDocuments:', error);\n    return [];\n  }\n};\n\nexport const getDocumentById = async (id: string): Promise<Document | null> => {\n  try {\n    const { data, error } = await supabase\n      .from('documents')\n      .select('*')\n      .eq('id', id)\n      .single();\n\n    if (error) {\n      console.error('Error fetching document:', error);\n      return null;\n    }\n\n    return data;\n  } catch (error) {\n    console.error('Error in getDocumentById:', error);\n    return null;\n  }\n};\n\nexport const getDocumentChunks = async (documentId: string): Promise<DocumentChunk[]> => {\n  try {\n    const { data, error } = await supabase\n      .from('document_chunks')\n      .select('*')\n      .eq('document_id', documentId)\n      .order('chunk_index', { ascending: true });\n\n    if (error) {\n      console.error('Error fetching document chunks:', error);\n      return [];\n    }\n\n    return data || [];\n  } catch (error) {\n    console.error('Error in getDocumentChunks:', error);\n    return [];\n  }\n};\n\n// Website operations\nexport const getWebsites = async (): Promise<Website[]> => {\n  try {\n    const { data, error } = await supabase\n      .from('websites')\n      .select('*')\n      .order('created_at', { ascending: false });\n\n    if (error) {\n      console.error('Error fetching websites:', error);\n      return [];\n    }\n\n    return data || [];\n  } catch (error) {\n    console.error('Error in getWebsites:', error);\n    return [];\n  }\n};\n\nexport const getWebsiteById = async (id: string): Promise<Website | null> => {\n  try {\n    const { data, error } = await supabase\n      .from('websites')\n      .select('*')\n      .eq('id', id)\n      .single();\n\n    if (error) {\n      console.error('Error fetching website:', error);\n      return null;\n    }\n\n    return data;\n  } catch (error) {\n    console.error('Error in getWebsiteById:', error);\n    return null;\n  }\n};\n\n// Query operations\nexport const saveQuery = async (query: Omit<Query, 'id' | 'created_at'>): Promise<Query | null> => {\n  try {\n    const { data, error } = await supabase\n      .from('queries')\n      .insert([query])\n      .select()\n      .single();\n\n    if (error) {\n      console.error('Error saving query:', error);\n      return null;\n    }\n\n    return data;\n  } catch (error) {\n    console.error('Error in saveQuery:', error);\n    return null;\n  }\n};\n\nexport const getRecentQueries = async (limit: number = 10): Promise<Query[]> => {\n  try {\n    const { data, error } = await supabase\n      .from('queries')\n      .select('*')\n      .order('created_at', { ascending: false })\n      .limit(limit);\n\n    if (error) {\n      console.error('Error fetching recent queries:', error);\n      return [];\n    }\n\n    return data || [];\n  } catch (error) {\n    console.error('Error in getRecentQueries:', error);\n    return [];\n  }\n};\n\n// Storage operations\nexport const uploadFile = async (\n  filePath: string,\n  file: File,\n  onProgress?: (progress: number) => void\n): Promise<string | null> => {\n  try {\n    console.log(`🔄 Starting file upload: ${filePath}`);\n    console.log(`📁 File details: ${file.name}, ${file.size} bytes, ${file.type}`);\n    \n    // Try multiple upload strategies to handle RLS issues\n    \n    // Strategy 1: Direct upload with auth context\n    try {\n      const { data, error } = await supabase.storage\n        .from('documents')\n        .upload(filePath, file, {\n          cacheControl: '3600',\n          upsert: true,  // Allow overwriting\n          contentType: file.type || 'application/octet-stream'\n        });\n\n      if (error) {\n        console.warn(`⚠️ Strategy 1 failed:`, error);\n        throw error;\n      }\n\n      // Get public URL\n      const { data: { publicUrl } } = supabase.storage\n        .from('documents')\n        .getPublicUrl(data.path);\n\n      console.log(`✅ Strategy 1 success: ${publicUrl}`);\n      return publicUrl;\n      \n    } catch (strategy1Error) {\n      console.warn(`Strategy 1 failed, trying Strategy 2:`, strategy1Error);\n      \n      // Strategy 2: Upload with simplified path and anonymous context\n      try {\n        // Create simpler file path\n        const simplePath = `${Date.now()}_${file.name}`;\n        \n        const { data, error } = await supabase.storage\n          .from('documents')\n          .upload(simplePath, file, {\n            upsert: true,\n            contentType: file.type\n          });\n\n        if (error) {\n          console.warn(`⚠️ Strategy 2 failed:`, error);\n          throw error;\n        }\n\n        const { data: { publicUrl } } = supabase.storage\n          .from('documents')\n          .getPublicUrl(data.path);\n\n        console.log(`✅ Strategy 2 success: ${publicUrl}`);\n        return publicUrl;\n        \n      } catch (strategy2Error) {\n        console.warn(`Strategy 2 failed, trying Strategy 3:`, strategy2Error);\n        \n        // Strategy 3: Use backend upload endpoint as fallback\n        try {\n          const formData = new FormData();\n          formData.append('file', file);\n          formData.append('uploaded_by', 'frontend_user');\n          \n          const response = await fetch('/api/upload-document', {\n            method: 'POST',\n            body: formData\n          });\n          \n          if (!response.ok) {\n            throw new Error(`Backend upload failed: ${response.statusText}`);\n          }\n          \n          const result = await response.json();\n          console.log(`✅ Strategy 3 success via backend:`, result);\n          \n          return result.storage_url || result.local_path || 'uploaded_via_backend';\n          \n        } catch (strategy3Error) {\n          console.error(`❌ All upload strategies failed:`, {\n            strategy1: strategy1Error,\n            strategy2: strategy2Error,\n            strategy3: strategy3Error\n          });\n          \n          // Return null to indicate failure\n          return null;\n        }\n      }\n    }\n    \n  } catch (error) {\n    console.error('❌ Critical upload error:', error);\n    return null;\n  }\n};\n\nexport const getFileUrl = (filePath: string): string => {\n  const { data: { publicUrl } } = supabase.storage\n    .from('documents')\n    .getPublicUrl(filePath);\n\n  return publicUrl;\n};\n\n// Chat Session types\nexport interface ChatMessage {\n  id: string;\n  content: string;\n  document_answer?: string;\n  website_answer?: string;\n  llm_model?: string;\n  sender: 'user' | 'ai';\n  loading?: boolean;\n  sources?: Array<any>;\n  document_sources?: Array<any>;\n  website_sources?: Array<any>;\n  timestamp?: string;\n  chatId?: string;\n  llm_fallback_used?: boolean;  // CORRECTED: Use llm_fallback_used instead of llmFallbackUsed\n}\n\nexport interface ChatSession {\n  id: string;\n  user_id?: string;\n  title: string;\n  messages: ChatMessage[];\n  model_used: string;\n  created_at: string;\n  updated_at: string;\n  tags?: string[];\n  has_document: boolean;\n  has_website: boolean;\n}\n\n// Chat Session operations\nexport const createChatSession = async (title: string = 'New Chat', modelUsed: string = 'gemini-2.0-flash'): Promise<ChatSession | null> => {\n  try {\n    const { data, error } = await supabase\n      .from('chat_sessions')\n      .insert([{\n        title,\n        messages: [],\n        model_used: modelUsed,\n        has_document: false,\n        has_website: false\n      }])\n      .select()\n      .single();\n\n    if (error) {\n      console.error('Error creating chat session:', error);\n      return null;\n    }\n\n    return {\n      ...data,\n      messages: data.messages || []\n    };\n  } catch (error) {\n    console.error('Error in createChatSession:', error);\n    return null;\n  }\n};\n\nexport const getChatSessions = async (userId?: string): Promise<ChatSession[]> => {\n  try {\n    console.log('Attempting to fetch chat sessions...');\n\n    // First, try to check if the table exists with a simple query\n    const { data: tableCheck, error: tableError } = await supabase\n      .from('chat_sessions')\n      .select('id')\n      .limit(1);\n\n    if (tableError) {\n      console.warn('Chat sessions table not accessible:', tableError.message);\n      console.log('Returning empty chat sessions array');\n      return [];\n    }\n\n    // If table exists, try the full query with basic columns only\n    let query = supabase\n      .from('chat_sessions')\n      .select('id, title, created_at, updated_at')\n      .order('updated_at', { ascending: false })\n      .limit(10); // Reduced limit\n\n    if (userId) {\n      query = query.eq('user_id', userId);\n    }\n\n    const { data, error } = await query;\n\n    if (error) {\n      console.warn('Chat sessions query failed:', error.message);\n      return [];\n    }\n\n    // Process and return data with safe defaults\n    const sessions = (data || []).map(session => ({\n      id: session.id,\n      title: session.title || 'Untitled Chat',\n      created_at: session.created_at,\n      updated_at: session.updated_at,\n      model_used: 'gemini-2.0-flash',\n      has_document: false,\n      has_website: false,\n      messages: []\n    }));\n\n    console.log(`Successfully loaded ${sessions.length} chat sessions`);\n    return sessions;\n\n  } catch (error: any) {\n    console.warn('Error in getChatSessions:', error.message || error);\n    return [];\n  }\n};\n\nexport const getChatSessionById = async (id: string): Promise<ChatSession | null> => {\n  try {\n    const { data, error } = await supabase\n      .from('chat_sessions')\n      .select('*')\n      .eq('id', id)\n      .single();\n\n    if (error) {\n      console.error('Error fetching chat session:', error);\n      return null;\n    }\n\n    return {\n      ...data,\n      messages: data.messages || []\n    };\n  } catch (error) {\n    console.error('Error in getChatSessionById:', error);\n    return null;\n  }\n};\n\nexport const updateChatSession = async (id: string, updates: Partial<ChatSession>): Promise<ChatSession | null> => {\n  try {\n    const { data, error } = await supabase\n      .from('chat_sessions')\n      .update(updates)\n      .eq('id', id)\n      .select()\n      .single();\n\n    if (error) {\n      console.error('Error updating chat session:', error);\n      return null;\n    }\n\n    return {\n      ...data,\n      messages: data.messages || []\n    };\n  } catch (error) {\n    console.error('Error in updateChatSession:', error);\n    return null;\n  }\n};\n\nexport const updateChatTitle = async (id: string, title: string): Promise<boolean> => {\n  try {\n    const { error } = await supabase\n      .from('chat_sessions')\n      .update({ title })\n      .eq('id', id);\n\n    if (error) {\n      console.error('Error updating chat title:', error);\n      return false;\n    }\n\n    return true;\n  } catch (error) {\n    console.error('Error in updateChatTitle:', error);\n    return false;\n  }\n};\n\nexport const deleteChatSession = async (id: string): Promise<boolean> => {\n  try {\n    const { error } = await supabase\n      .from('chat_sessions')\n      .delete()\n      .eq('id', id);\n\n    if (error) {\n      console.error('Error deleting chat session:', error);\n      return false;\n    }\n\n    return true;\n  } catch (error) {\n    console.error('Error in deleteChatSession:', error);\n    return false;\n  }\n};\n\nexport const saveChatMessages = async (chatId: string, messages: ChatMessage[]): Promise<boolean> => {\n  try {\n    // Ensure all message fields are preserved when saving to Supabase\n    const sanitizedMessages = messages.map(msg => ({\n      id: msg.id,\n      content: msg.content,\n      document_answer: msg.document_answer || undefined,\n      website_answer: msg.website_answer || undefined,\n      llm_model: msg.llm_model,\n      sender: msg.sender,\n      loading: msg.loading,\n      sources: msg.sources || undefined,\n      document_sources: msg.document_sources || undefined,\n      website_sources: msg.website_sources || undefined,\n      timestamp: msg.timestamp,\n      chatId: msg.chatId,\n      llmFallbackUsed: msg.llmFallbackUsed\n    }));\n\n    // Check if chat has documents or websites in the messages\n    const hasDocument = sanitizedMessages.some(msg => \n      msg.document_answer || \n      (msg.document_sources && msg.document_sources.length > 0) ||\n      (msg.sources && msg.sources.some((s: any) => s.source_type === 'document'))\n    );\n    \n    const hasWebsite = sanitizedMessages.some(msg => \n      msg.website_answer || \n      (msg.website_sources && msg.website_sources.length > 0) ||\n      (msg.sources && msg.sources.some((s: any) => s.source_type === 'website'))\n    );\n\n    console.log('Saving chat messages:', {\n      messageCount: sanitizedMessages.length,\n      hasDocument,\n      hasWebsite,\n      messagesWithDocAnswer: sanitizedMessages.filter(m => m.document_answer).length,\n      messagesWithWebAnswer: sanitizedMessages.filter(m => m.website_answer).length\n    });\n\n    const { error } = await supabase\n      .from('chat_sessions')\n      .update({ \n        messages: sanitizedMessages,\n        has_document: hasDocument,\n        has_website: hasWebsite\n      })\n      .eq('id', chatId);\n\n    if (error) {\n      console.error('Error saving chat messages:', error);\n      return false;\n    }\n\n    return true;\n  } catch (error) {\n    console.error('Error in saveChatMessages:', error);\n    return false;\n  }\n};\n\nexport const clearAllChatSessions = async (userId?: string): Promise<boolean> => {\n  try {\n    let query = supabase.from('chat_sessions').delete();\n    \n    if (userId) {\n      query = query.eq('user_id', userId);\n    }\n\n    const { error } = await query;\n\n    if (error) {\n      console.error('Error clearing chat sessions:', error);\n      return false;\n    }\n\n    return true;\n  } catch (error) {\n    console.error('Error in clearAllChatSessions:', error);\n    return false;\n  }\n};\n\nexport default supabase;\n"], "mappings": "AAAA;AACA,OAAOA,cAAc,IAAIC,uBAAuB,QAAQ,yBAAyB;;AAEjF;AACA,OAAO,MAAMC,QAAQ,GAAGF,cAAc;;AAEtC;AACA,OAAO,MAAMG,sBAAsB,GAAGF,uBAAuB;;AAE7D;;AA6BA;;AAwBA;;AAYA;AACA,OAAO,MAAMG,YAAY,GAAG,MAAAA,CAAA,KAAiC;EAC3D,IAAI;IACF,MAAM;MAAEC,IAAI;MAAEC;IAAM,CAAC,GAAG,MAAMJ,QAAQ,CACnCK,IAAI,CAAC,WAAW,CAAC,CACjBC,MAAM,CAAC,GAAG,CAAC,CACXC,KAAK,CAAC,YAAY,EAAE;MAAEC,SAAS,EAAE;IAAM,CAAC,CAAC;IAE5C,IAAIJ,KAAK,EAAE;MACTK,OAAO,CAACL,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAO,EAAE;IACX;IAEA,OAAOD,IAAI,IAAI,EAAE;EACnB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdK,OAAO,CAACL,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;IAC9C,OAAO,EAAE;EACX;AACF,CAAC;AAED,OAAO,MAAMM,eAAe,GAAG,MAAOC,EAAU,IAA+B;EAC7E,IAAI;IACF,MAAM;MAAER,IAAI;MAAEC;IAAM,CAAC,GAAG,MAAMJ,QAAQ,CACnCK,IAAI,CAAC,WAAW,CAAC,CACjBC,MAAM,CAAC,GAAG,CAAC,CACXM,EAAE,CAAC,IAAI,EAAED,EAAE,CAAC,CACZE,MAAM,CAAC,CAAC;IAEX,IAAIT,KAAK,EAAE;MACTK,OAAO,CAACL,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAO,IAAI;IACb;IAEA,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdK,OAAO,CAACL,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACjD,OAAO,IAAI;EACb;AACF,CAAC;AAED,OAAO,MAAMU,iBAAiB,GAAG,MAAOC,UAAkB,IAA+B;EACvF,IAAI;IACF,MAAM;MAAEZ,IAAI;MAAEC;IAAM,CAAC,GAAG,MAAMJ,QAAQ,CACnCK,IAAI,CAAC,iBAAiB,CAAC,CACvBC,MAAM,CAAC,GAAG,CAAC,CACXM,EAAE,CAAC,aAAa,EAAEG,UAAU,CAAC,CAC7BR,KAAK,CAAC,aAAa,EAAE;MAAEC,SAAS,EAAE;IAAK,CAAC,CAAC;IAE5C,IAAIJ,KAAK,EAAE;MACTK,OAAO,CAACL,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,OAAO,EAAE;IACX;IAEA,OAAOD,IAAI,IAAI,EAAE;EACnB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdK,OAAO,CAACL,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACnD,OAAO,EAAE;EACX;AACF,CAAC;;AAED;AACA,OAAO,MAAMY,WAAW,GAAG,MAAAA,CAAA,KAAgC;EACzD,IAAI;IACF,MAAM;MAAEb,IAAI;MAAEC;IAAM,CAAC,GAAG,MAAMJ,QAAQ,CACnCK,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC,GAAG,CAAC,CACXC,KAAK,CAAC,YAAY,EAAE;MAAEC,SAAS,EAAE;IAAM,CAAC,CAAC;IAE5C,IAAIJ,KAAK,EAAE;MACTK,OAAO,CAACL,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MAChD,OAAO,EAAE;IACX;IAEA,OAAOD,IAAI,IAAI,EAAE;EACnB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdK,OAAO,CAACL,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;IAC7C,OAAO,EAAE;EACX;AACF,CAAC;AAED,OAAO,MAAMa,cAAc,GAAG,MAAON,EAAU,IAA8B;EAC3E,IAAI;IACF,MAAM;MAAER,IAAI;MAAEC;IAAM,CAAC,GAAG,MAAMJ,QAAQ,CACnCK,IAAI,CAAC,UAAU,CAAC,CAChBC,MAAM,CAAC,GAAG,CAAC,CACXM,EAAE,CAAC,IAAI,EAAED,EAAE,CAAC,CACZE,MAAM,CAAC,CAAC;IAEX,IAAIT,KAAK,EAAE;MACTK,OAAO,CAACL,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MAC/C,OAAO,IAAI;IACb;IAEA,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdK,OAAO,CAACL,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAChD,OAAO,IAAI;EACb;AACF,CAAC;;AAED;AACA,OAAO,MAAMc,SAAS,GAAG,MAAOC,KAAuC,IAA4B;EACjG,IAAI;IACF,MAAM;MAAEhB,IAAI;MAAEC;IAAM,CAAC,GAAG,MAAMJ,QAAQ,CACnCK,IAAI,CAAC,SAAS,CAAC,CACfe,MAAM,CAAC,CAACD,KAAK,CAAC,CAAC,CACfb,MAAM,CAAC,CAAC,CACRO,MAAM,CAAC,CAAC;IAEX,IAAIT,KAAK,EAAE;MACTK,OAAO,CAACL,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;MAC3C,OAAO,IAAI;IACb;IAEA,OAAOD,IAAI;EACb,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdK,OAAO,CAACL,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;IAC3C,OAAO,IAAI;EACb;AACF,CAAC;AAED,OAAO,MAAMiB,gBAAgB,GAAG,MAAAA,CAAOC,KAAa,GAAG,EAAE,KAAuB;EAC9E,IAAI;IACF,MAAM;MAAEnB,IAAI;MAAEC;IAAM,CAAC,GAAG,MAAMJ,QAAQ,CACnCK,IAAI,CAAC,SAAS,CAAC,CACfC,MAAM,CAAC,GAAG,CAAC,CACXC,KAAK,CAAC,YAAY,EAAE;MAAEC,SAAS,EAAE;IAAM,CAAC,CAAC,CACzCc,KAAK,CAACA,KAAK,CAAC;IAEf,IAAIlB,KAAK,EAAE;MACTK,OAAO,CAACL,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,OAAO,EAAE;IACX;IAEA,OAAOD,IAAI,IAAI,EAAE;EACnB,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdK,OAAO,CAACL,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IAClD,OAAO,EAAE;EACX;AACF,CAAC;;AAED;AACA,OAAO,MAAMmB,UAAU,GAAG,MAAAA,CACxBC,QAAgB,EAChBC,IAAU,EACVC,UAAuC,KACZ;EAC3B,IAAI;IACFjB,OAAO,CAACkB,GAAG,CAAC,4BAA4BH,QAAQ,EAAE,CAAC;IACnDf,OAAO,CAACkB,GAAG,CAAC,oBAAoBF,IAAI,CAACG,IAAI,KAAKH,IAAI,CAACI,IAAI,WAAWJ,IAAI,CAACK,IAAI,EAAE,CAAC;;IAE9E;;IAEA;IACA,IAAI;MACF,MAAM;QAAE3B,IAAI;QAAEC;MAAM,CAAC,GAAG,MAAMJ,QAAQ,CAAC+B,OAAO,CAC3C1B,IAAI,CAAC,WAAW,CAAC,CACjB2B,MAAM,CAACR,QAAQ,EAAEC,IAAI,EAAE;QACtBQ,YAAY,EAAE,MAAM;QACpBC,MAAM,EAAE,IAAI;QAAG;QACfC,WAAW,EAAEV,IAAI,CAACK,IAAI,IAAI;MAC5B,CAAC,CAAC;MAEJ,IAAI1B,KAAK,EAAE;QACTK,OAAO,CAAC2B,IAAI,CAAC,uBAAuB,EAAEhC,KAAK,CAAC;QAC5C,MAAMA,KAAK;MACb;;MAEA;MACA,MAAM;QAAED,IAAI,EAAE;UAAEkC;QAAU;MAAE,CAAC,GAAGrC,QAAQ,CAAC+B,OAAO,CAC7C1B,IAAI,CAAC,WAAW,CAAC,CACjBiC,YAAY,CAACnC,IAAI,CAACoC,IAAI,CAAC;MAE1B9B,OAAO,CAACkB,GAAG,CAAC,yBAAyBU,SAAS,EAAE,CAAC;MACjD,OAAOA,SAAS;IAElB,CAAC,CAAC,OAAOG,cAAc,EAAE;MACvB/B,OAAO,CAAC2B,IAAI,CAAC,uCAAuC,EAAEI,cAAc,CAAC;;MAErE;MACA,IAAI;QACF;QACA,MAAMC,UAAU,GAAG,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,IAAIlB,IAAI,CAACG,IAAI,EAAE;QAE/C,MAAM;UAAEzB,IAAI;UAAEC;QAAM,CAAC,GAAG,MAAMJ,QAAQ,CAAC+B,OAAO,CAC3C1B,IAAI,CAAC,WAAW,CAAC,CACjB2B,MAAM,CAACS,UAAU,EAAEhB,IAAI,EAAE;UACxBS,MAAM,EAAE,IAAI;UACZC,WAAW,EAAEV,IAAI,CAACK;QACpB,CAAC,CAAC;QAEJ,IAAI1B,KAAK,EAAE;UACTK,OAAO,CAAC2B,IAAI,CAAC,uBAAuB,EAAEhC,KAAK,CAAC;UAC5C,MAAMA,KAAK;QACb;QAEA,MAAM;UAAED,IAAI,EAAE;YAAEkC;UAAU;QAAE,CAAC,GAAGrC,QAAQ,CAAC+B,OAAO,CAC7C1B,IAAI,CAAC,WAAW,CAAC,CACjBiC,YAAY,CAACnC,IAAI,CAACoC,IAAI,CAAC;QAE1B9B,OAAO,CAACkB,GAAG,CAAC,yBAAyBU,SAAS,EAAE,CAAC;QACjD,OAAOA,SAAS;MAElB,CAAC,CAAC,OAAOO,cAAc,EAAE;QACvBnC,OAAO,CAAC2B,IAAI,CAAC,uCAAuC,EAAEQ,cAAc,CAAC;;QAErE;QACA,IAAI;UACF,MAAMC,QAAQ,GAAG,IAAIC,QAAQ,CAAC,CAAC;UAC/BD,QAAQ,CAACE,MAAM,CAAC,MAAM,EAAEtB,IAAI,CAAC;UAC7BoB,QAAQ,CAACE,MAAM,CAAC,aAAa,EAAE,eAAe,CAAC;UAE/C,MAAMC,QAAQ,GAAG,MAAMC,KAAK,CAAC,sBAAsB,EAAE;YACnDC,MAAM,EAAE,MAAM;YACdC,IAAI,EAAEN;UACR,CAAC,CAAC;UAEF,IAAI,CAACG,QAAQ,CAACI,EAAE,EAAE;YAChB,MAAM,IAAIC,KAAK,CAAC,0BAA0BL,QAAQ,CAACM,UAAU,EAAE,CAAC;UAClE;UAEA,MAAMC,MAAM,GAAG,MAAMP,QAAQ,CAACQ,IAAI,CAAC,CAAC;UACpC/C,OAAO,CAACkB,GAAG,CAAC,mCAAmC,EAAE4B,MAAM,CAAC;UAExD,OAAOA,MAAM,CAACE,WAAW,IAAIF,MAAM,CAACG,UAAU,IAAI,sBAAsB;QAE1E,CAAC,CAAC,OAAOC,cAAc,EAAE;UACvBlD,OAAO,CAACL,KAAK,CAAC,iCAAiC,EAAE;YAC/CwD,SAAS,EAAEpB,cAAc;YACzBqB,SAAS,EAAEjB,cAAc;YACzBkB,SAAS,EAAEH;UACb,CAAC,CAAC;;UAEF;UACA,OAAO,IAAI;QACb;MACF;IACF;EAEF,CAAC,CAAC,OAAOvD,KAAK,EAAE;IACdK,OAAO,CAACL,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;IAChD,OAAO,IAAI;EACb;AACF,CAAC;AAED,OAAO,MAAM2D,UAAU,GAAIvC,QAAgB,IAAa;EACtD,MAAM;IAAErB,IAAI,EAAE;MAAEkC;IAAU;EAAE,CAAC,GAAGrC,QAAQ,CAAC+B,OAAO,CAC7C1B,IAAI,CAAC,WAAW,CAAC,CACjBiC,YAAY,CAACd,QAAQ,CAAC;EAEzB,OAAOa,SAAS;AAClB,CAAC;;AAED;;AA8BA;AACA,OAAO,MAAM2B,iBAAiB,GAAG,MAAAA,CAAOC,KAAa,GAAG,UAAU,EAAEC,SAAiB,GAAG,kBAAkB,KAAkC;EAC1I,IAAI;IACF,MAAM;MAAE/D,IAAI;MAAEC;IAAM,CAAC,GAAG,MAAMJ,QAAQ,CACnCK,IAAI,CAAC,eAAe,CAAC,CACrBe,MAAM,CAAC,CAAC;MACP6C,KAAK;MACLE,QAAQ,EAAE,EAAE;MACZC,UAAU,EAAEF,SAAS;MACrBG,YAAY,EAAE,KAAK;MACnBC,WAAW,EAAE;IACf,CAAC,CAAC,CAAC,CACFhE,MAAM,CAAC,CAAC,CACRO,MAAM,CAAC,CAAC;IAEX,IAAIT,KAAK,EAAE;MACTK,OAAO,CAACL,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAO,IAAI;IACb;IAEA,OAAO;MACL,GAAGD,IAAI;MACPgE,QAAQ,EAAEhE,IAAI,CAACgE,QAAQ,IAAI;IAC7B,CAAC;EACH,CAAC,CAAC,OAAO/D,KAAK,EAAE;IACdK,OAAO,CAACL,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACnD,OAAO,IAAI;EACb;AACF,CAAC;AAED,OAAO,MAAMmE,eAAe,GAAG,MAAOC,MAAe,IAA6B;EAChF,IAAI;IACF/D,OAAO,CAACkB,GAAG,CAAC,sCAAsC,CAAC;;IAEnD;IACA,MAAM;MAAExB,IAAI,EAAEsE,UAAU;MAAErE,KAAK,EAAEsE;IAAW,CAAC,GAAG,MAAM1E,QAAQ,CAC3DK,IAAI,CAAC,eAAe,CAAC,CACrBC,MAAM,CAAC,IAAI,CAAC,CACZgB,KAAK,CAAC,CAAC,CAAC;IAEX,IAAIoD,UAAU,EAAE;MACdjE,OAAO,CAAC2B,IAAI,CAAC,qCAAqC,EAAEsC,UAAU,CAACC,OAAO,CAAC;MACvElE,OAAO,CAACkB,GAAG,CAAC,qCAAqC,CAAC;MAClD,OAAO,EAAE;IACX;;IAEA;IACA,IAAIR,KAAK,GAAGnB,QAAQ,CACjBK,IAAI,CAAC,eAAe,CAAC,CACrBC,MAAM,CAAC,mCAAmC,CAAC,CAC3CC,KAAK,CAAC,YAAY,EAAE;MAAEC,SAAS,EAAE;IAAM,CAAC,CAAC,CACzCc,KAAK,CAAC,EAAE,CAAC,CAAC,CAAC;;IAEd,IAAIkD,MAAM,EAAE;MACVrD,KAAK,GAAGA,KAAK,CAACP,EAAE,CAAC,SAAS,EAAE4D,MAAM,CAAC;IACrC;IAEA,MAAM;MAAErE,IAAI;MAAEC;IAAM,CAAC,GAAG,MAAMe,KAAK;IAEnC,IAAIf,KAAK,EAAE;MACTK,OAAO,CAAC2B,IAAI,CAAC,6BAA6B,EAAEhC,KAAK,CAACuE,OAAO,CAAC;MAC1D,OAAO,EAAE;IACX;;IAEA;IACA,MAAMC,QAAQ,GAAG,CAACzE,IAAI,IAAI,EAAE,EAAE0E,GAAG,CAACC,OAAO,KAAK;MAC5CnE,EAAE,EAAEmE,OAAO,CAACnE,EAAE;MACdsD,KAAK,EAAEa,OAAO,CAACb,KAAK,IAAI,eAAe;MACvCc,UAAU,EAAED,OAAO,CAACC,UAAU;MAC9BC,UAAU,EAAEF,OAAO,CAACE,UAAU;MAC9BZ,UAAU,EAAE,kBAAkB;MAC9BC,YAAY,EAAE,KAAK;MACnBC,WAAW,EAAE,KAAK;MAClBH,QAAQ,EAAE;IACZ,CAAC,CAAC,CAAC;IAEH1D,OAAO,CAACkB,GAAG,CAAC,uBAAuBiD,QAAQ,CAACK,MAAM,gBAAgB,CAAC;IACnE,OAAOL,QAAQ;EAEjB,CAAC,CAAC,OAAOxE,KAAU,EAAE;IACnBK,OAAO,CAAC2B,IAAI,CAAC,2BAA2B,EAAEhC,KAAK,CAACuE,OAAO,IAAIvE,KAAK,CAAC;IACjE,OAAO,EAAE;EACX;AACF,CAAC;AAED,OAAO,MAAM8E,kBAAkB,GAAG,MAAOvE,EAAU,IAAkC;EACnF,IAAI;IACF,MAAM;MAAER,IAAI;MAAEC;IAAM,CAAC,GAAG,MAAMJ,QAAQ,CACnCK,IAAI,CAAC,eAAe,CAAC,CACrBC,MAAM,CAAC,GAAG,CAAC,CACXM,EAAE,CAAC,IAAI,EAAED,EAAE,CAAC,CACZE,MAAM,CAAC,CAAC;IAEX,IAAIT,KAAK,EAAE;MACTK,OAAO,CAACL,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAO,IAAI;IACb;IAEA,OAAO;MACL,GAAGD,IAAI;MACPgE,QAAQ,EAAEhE,IAAI,CAACgE,QAAQ,IAAI;IAC7B,CAAC;EACH,CAAC,CAAC,OAAO/D,KAAK,EAAE;IACdK,OAAO,CAACL,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;IACpD,OAAO,IAAI;EACb;AACF,CAAC;AAED,OAAO,MAAM+E,iBAAiB,GAAG,MAAAA,CAAOxE,EAAU,EAAEyE,OAA6B,KAAkC;EACjH,IAAI;IACF,MAAM;MAAEjF,IAAI;MAAEC;IAAM,CAAC,GAAG,MAAMJ,QAAQ,CACnCK,IAAI,CAAC,eAAe,CAAC,CACrBgF,MAAM,CAACD,OAAO,CAAC,CACfxE,EAAE,CAAC,IAAI,EAAED,EAAE,CAAC,CACZL,MAAM,CAAC,CAAC,CACRO,MAAM,CAAC,CAAC;IAEX,IAAIT,KAAK,EAAE;MACTK,OAAO,CAACL,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAO,IAAI;IACb;IAEA,OAAO;MACL,GAAGD,IAAI;MACPgE,QAAQ,EAAEhE,IAAI,CAACgE,QAAQ,IAAI;IAC7B,CAAC;EACH,CAAC,CAAC,OAAO/D,KAAK,EAAE;IACdK,OAAO,CAACL,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACnD,OAAO,IAAI;EACb;AACF,CAAC;AAED,OAAO,MAAMkF,eAAe,GAAG,MAAAA,CAAO3E,EAAU,EAAEsD,KAAa,KAAuB;EACpF,IAAI;IACF,MAAM;MAAE7D;IAAM,CAAC,GAAG,MAAMJ,QAAQ,CAC7BK,IAAI,CAAC,eAAe,CAAC,CACrBgF,MAAM,CAAC;MAAEpB;IAAM,CAAC,CAAC,CACjBrD,EAAE,CAAC,IAAI,EAAED,EAAE,CAAC;IAEf,IAAIP,KAAK,EAAE;MACTK,OAAO,CAACL,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb,CAAC,CAAC,OAAOA,KAAK,EAAE;IACdK,OAAO,CAACL,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;IACjD,OAAO,KAAK;EACd;AACF,CAAC;AAED,OAAO,MAAMmF,iBAAiB,GAAG,MAAO5E,EAAU,IAAuB;EACvE,IAAI;IACF,MAAM;MAAEP;IAAM,CAAC,GAAG,MAAMJ,QAAQ,CAC7BK,IAAI,CAAC,eAAe,CAAC,CACrBmF,MAAM,CAAC,CAAC,CACR5E,EAAE,CAAC,IAAI,EAAED,EAAE,CAAC;IAEf,IAAIP,KAAK,EAAE;MACTK,OAAO,CAACL,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACpD,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb,CAAC,CAAC,OAAOA,KAAK,EAAE;IACdK,OAAO,CAACL,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;IACnD,OAAO,KAAK;EACd;AACF,CAAC;AAED,OAAO,MAAMqF,gBAAgB,GAAG,MAAAA,CAAOC,MAAc,EAAEvB,QAAuB,KAAuB;EACnG,IAAI;IACF;IACA,MAAMwB,iBAAiB,GAAGxB,QAAQ,CAACU,GAAG,CAACe,GAAG,KAAK;MAC7CjF,EAAE,EAAEiF,GAAG,CAACjF,EAAE;MACVkF,OAAO,EAAED,GAAG,CAACC,OAAO;MACpBC,eAAe,EAAEF,GAAG,CAACE,eAAe,IAAIC,SAAS;MACjDC,cAAc,EAAEJ,GAAG,CAACI,cAAc,IAAID,SAAS;MAC/CE,SAAS,EAAEL,GAAG,CAACK,SAAS;MACxBC,MAAM,EAAEN,GAAG,CAACM,MAAM;MAClBC,OAAO,EAAEP,GAAG,CAACO,OAAO;MACpBC,OAAO,EAAER,GAAG,CAACQ,OAAO,IAAIL,SAAS;MACjCM,gBAAgB,EAAET,GAAG,CAACS,gBAAgB,IAAIN,SAAS;MACnDO,eAAe,EAAEV,GAAG,CAACU,eAAe,IAAIP,SAAS;MACjDQ,SAAS,EAAEX,GAAG,CAACW,SAAS;MACxBb,MAAM,EAAEE,GAAG,CAACF,MAAM;MAClBc,eAAe,EAAEZ,GAAG,CAACY;IACvB,CAAC,CAAC,CAAC;;IAEH;IACA,MAAMC,WAAW,GAAGd,iBAAiB,CAACe,IAAI,CAACd,GAAG,IAC5CA,GAAG,CAACE,eAAe,IAClBF,GAAG,CAACS,gBAAgB,IAAIT,GAAG,CAACS,gBAAgB,CAACpB,MAAM,GAAG,CAAE,IACxDW,GAAG,CAACQ,OAAO,IAAIR,GAAG,CAACQ,OAAO,CAACM,IAAI,CAAEC,CAAM,IAAKA,CAAC,CAACC,WAAW,KAAK,UAAU,CAC3E,CAAC;IAED,MAAMC,UAAU,GAAGlB,iBAAiB,CAACe,IAAI,CAACd,GAAG,IAC3CA,GAAG,CAACI,cAAc,IACjBJ,GAAG,CAACU,eAAe,IAAIV,GAAG,CAACU,eAAe,CAACrB,MAAM,GAAG,CAAE,IACtDW,GAAG,CAACQ,OAAO,IAAIR,GAAG,CAACQ,OAAO,CAACM,IAAI,CAAEC,CAAM,IAAKA,CAAC,CAACC,WAAW,KAAK,SAAS,CAC1E,CAAC;IAEDnG,OAAO,CAACkB,GAAG,CAAC,uBAAuB,EAAE;MACnCmF,YAAY,EAAEnB,iBAAiB,CAACV,MAAM;MACtCwB,WAAW;MACXI,UAAU;MACVE,qBAAqB,EAAEpB,iBAAiB,CAACqB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACnB,eAAe,CAAC,CAACb,MAAM;MAC9EiC,qBAAqB,EAAEvB,iBAAiB,CAACqB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACjB,cAAc,CAAC,CAACf;IACzE,CAAC,CAAC;IAEF,MAAM;MAAE7E;IAAM,CAAC,GAAG,MAAMJ,QAAQ,CAC7BK,IAAI,CAAC,eAAe,CAAC,CACrBgF,MAAM,CAAC;MACNlB,QAAQ,EAAEwB,iBAAiB;MAC3BtB,YAAY,EAAEoC,WAAW;MACzBnC,WAAW,EAAEuC;IACf,CAAC,CAAC,CACDjG,EAAE,CAAC,IAAI,EAAE8E,MAAM,CAAC;IAEnB,IAAItF,KAAK,EAAE;MACTK,OAAO,CAACL,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACnD,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb,CAAC,CAAC,OAAOA,KAAK,EAAE;IACdK,OAAO,CAACL,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;IAClD,OAAO,KAAK;EACd;AACF,CAAC;AAED,OAAO,MAAM+G,oBAAoB,GAAG,MAAO3C,MAAe,IAAuB;EAC/E,IAAI;IACF,IAAIrD,KAAK,GAAGnB,QAAQ,CAACK,IAAI,CAAC,eAAe,CAAC,CAACmF,MAAM,CAAC,CAAC;IAEnD,IAAIhB,MAAM,EAAE;MACVrD,KAAK,GAAGA,KAAK,CAACP,EAAE,CAAC,SAAS,EAAE4D,MAAM,CAAC;IACrC;IAEA,MAAM;MAAEpE;IAAM,CAAC,GAAG,MAAMe,KAAK;IAE7B,IAAIf,KAAK,EAAE;MACTK,OAAO,CAACL,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAO,KAAK;IACd;IAEA,OAAO,IAAI;EACb,CAAC,CAAC,OAAOA,KAAK,EAAE;IACdK,OAAO,CAACL,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;IACtD,OAAO,KAAK;EACd;AACF,CAAC;AAED,eAAeJ,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module"}