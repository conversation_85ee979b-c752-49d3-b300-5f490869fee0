export interface ChatMessage {
  id: string;
  content: string;
  document_answer?: string;
  website_answer?: string;
  llm_model?: string;
  sender: 'user' | 'ai';
  loading?: boolean;
  sources?: Array<any>;
  document_sources?: Array<any>;
  website_sources?: Array<any>;
  timestamp?: string;
  chatId?: string;
  llm_fallback_used?: boolean;  // CORRECTED: Use llm_fallback_used instead of llmFallbackUsed
}

export interface ChatSession {
  id: string;
  user_id?: string;
  title: string;
  messages: ChatMessage[];
  model_used: string;
  created_at: string;
  updated_at: string;
  tags?: string[];
  has_document: boolean;
  has_website: boolean;
}

export interface ChatGroup {
  label: string;
  chats: ChatSession[];
}

export type TimeGroup = 'today' | 'yesterday' | 'last7days' | 'thisMonth' | 'lastMonth' | 'older'; 