#!/usr/bin/env python3
"""
Quick content check to see what's available in documents and websites
"""

import requests

def check_content():
    """Check what content is available for specific queries."""
    
    queries = [
        "FSDS",
        "Fire and Smoke Detection System", 
        "rapid response app",
        "quantum computing space"
    ]
    
    for query in queries:
        print(f"\n{'='*50}")
        print(f"🔍 Testing query: '{query}'")
        print(f"{'='*50}")
        
        try:
            response = requests.post(
                "http://localhost:8000/api/query",
                json={
                    "query": query,
                    "model": "gemini-2.0-flash",
                    "fallback_enabled": True,
                    "extract_format": "paragraph",
                    "use_hybrid_search": True
                },
                headers={"Content-Type": "application/json"},
                timeout=30
            )
            
            if response.status_code == 200:
                data = response.json()
                
                doc_answer = data.get('document_answer') or ''
                web_answer = data.get('website_answer') or ''
                llm_fallback = data.get('llmFallbackUsed', False)
                doc_sources = len(data.get('document_sources', []))
                web_sources = len(data.get('website_sources', []))
                
                print(f"📄 Document Answer: {'✅' if doc_answer.strip() else '❌'} ({doc_sources} sources)")
                print(f"🌐 Website Answer: {'✅' if web_answer.strip() else '❌'} ({web_sources} sources)")
                print(f"🤖 LLM Fallback: {'✅' if llm_fallback else '❌'}")
                
                if doc_answer.strip():
                    print(f"📄 Doc Preview: {doc_answer[:100]}...")
                if web_answer.strip():
                    print(f"🌐 Web Preview: {web_answer[:100]}...")
                    
            else:
                print(f"❌ HTTP Error: {response.status_code}")
                
        except Exception as e:
            print(f"❌ Error: {e}")

if __name__ == "__main__":
    check_content()
