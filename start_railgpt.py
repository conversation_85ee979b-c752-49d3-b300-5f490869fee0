#!/usr/bin/env python3
"""
RailGPT Unified Server Startup Script
This script helps you start both backend and frontend servers easily.
"""

import subprocess
import sys
import os
import time
from pathlib import Path

def print_banner():
    print("""
    ╔══════════════════════════════════════════════════╗
    ║               🚀 RailGPT Server                  ║
    ║          Unified Backend & Frontend              ║
    ╚══════════════════════════════════════════════════╝
    """)

def check_requirements():
    """Check if required directories exist"""
    backend_dir = Path("backend")
    frontend_dir = Path("frontend")
    
    if not backend_dir.exists():
        print("❌ Backend directory not found!")
        return False
    
    if not frontend_dir.exists():
        print("❌ Frontend directory not found!")
        return False
    
    # Check for key files
    if not (backend_dir / "server.py").exists():
        print("❌ server.py not found in backend directory!")
        return False
    
    if not (frontend_dir / "package.json").exists():
        print("❌ package.json not found in frontend directory!")
        return False
    
    return True

def start_backend_only():
    """Start only the backend server with choice of method"""
    print("🔧 Choose backend startup method:")
    print("1. python server.py (recommended)")
    print("2. uvicorn server:app --reload")
    
    choice = input("Enter choice (1 or 2): ").strip()
    
    backend_dir = Path("backend")
    os.chdir(backend_dir)
    
    try:
        if choice == "1":
            print("🚀 Starting with: python server.py")
            subprocess.run([sys.executable, "server.py"])
        elif choice == "2":
            print("🚀 Starting with: uvicorn server:app --reload")
            subprocess.run(["uvicorn", "server:app", "--reload", "--host", "0.0.0.0", "--port", "8000"])
        else:
            print("❌ Invalid choice. Using default: python server.py")
            subprocess.run([sys.executable, "server.py"])
            
    except KeyboardInterrupt:
        print("\n🛑 Backend server stopped by user")
    except Exception as e:
        print(f"❌ Failed to start backend: {e}")
    finally:
        os.chdir("..")

def main():
    print_banner()
    
    if not check_requirements():
        print("❌ Requirements check failed. Please ensure you're in the correct directory.")
        sys.exit(1)
    
    print("🔧 What would you like to start?")
    print("1. Backend only with python server.py")
    print("2. Backend only with uvicorn server:app --reload")
    print("3. Exit")
    
    choice = input("\nEnter your choice (1-3): ").strip()
    
    if choice == "1":
        print("🚀 Starting Backend with: python server.py")
        start_backend_only()
    elif choice == "2":
        print("🚀 Starting Backend with: uvicorn server:app --reload")
        backend_dir = Path("backend")
        os.chdir(backend_dir)
        try:
            subprocess.run(["uvicorn", "server:app", "--reload", "--host", "0.0.0.0", "--port", "8000"])
        except KeyboardInterrupt:
            print("\n🛑 Backend server stopped by user")
        finally:
            os.chdir("..")
    elif choice == "3":
        print("👋 Goodbye!")
        sys.exit(0)
    else:
        print("❌ Invalid choice. Please run the script again.")
        sys.exit(1)

if __name__ == "__main__":
    main() 