# 🚀 RailGPT Server Commands Reference

## ✅ **Recommended Server Startup Methods**

### **Option 1: Direct Python (Recommended)**
```bash
cd backend
python server.py
```
- ✅ **All routes working including category management**
- ✅ **Same functionality as uvicorn method**
- ✅ **Easier to run and debug**

### **Option 2: Uvicorn Command** 
```bash
cd backend
uvicorn server:app --reload
```
- ✅ **All routes working including category management**
- ✅ **Auto-reload on file changes**
- ✅ **Professional production-ready method**

### **Option 3: Startup Script**
```bash
# From project root directory
python start_railgpt.py
```
- ✅ **Interactive menu to choose startup method**
- ✅ **Simplified server management**

## 🔍 **Verification Commands**

### **Check if Category Routes are Loaded:**
```bash
cd backend
python -c "from server import app; [print(f'{route.methods} {route.path}') for route in app.routes if hasattr(route, 'path') and '/categories/' in route.path]"
```

### **Test Category API Endpoint:**
```bash
curl http://localhost:8000/api/categories/by-level/document/1
```

## 🎯 **Frontend Commands**

### **Start React Frontend:**
```bash
cd frontend
npm start
```

## 🚀 **Full Stack Development Setup**

### **Terminal 1 (Backend):**
```bash
cd backend
python server.py
```

### **Terminal 2 (Frontend):**
```bash
cd frontend  
npm start
```

## 📊 **Server URLs**

- **Frontend:** http://localhost:3000
- **Backend API:** http://localhost:8000  
- **API Documentation:** http://localhost:8000/docs
- **Category API Test:** http://localhost:8000/api/categories/test

## ✅ **Problem Solved**

The original issue where:
- `python server.py` → 404 errors for category endpoints
- `uvicorn server:app --reload` → 200 OK for category endpoints

**Has been fixed!** Both methods now work identically with all routes properly loaded. 