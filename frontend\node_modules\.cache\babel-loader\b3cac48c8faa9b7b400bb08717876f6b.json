{"ast": null, "code": "var _jsxFileName = \"C:\\\\IR App\\\\frontend\\\\src\\\\App.tsx\";\nimport React, { useState, useRef, useEffect } from 'react';\nimport './App.css';\nimport { sendQuery } from './services/api';\nimport LLMSelector, { DEFAULT_LLM_MODELS } from './components/ui/LLMSelector';\nimport InteractiveAnswer from './components/ui/InteractiveAnswer';\nimport ChatSidebar from './components/chat/ChatSidebar';\nimport TrainLoader from './components/ui/TrainLoader';\nimport VisualContent from './components/ui/VisualContent';\nimport { useChatContext } from './contexts/ChatContext';\n\n// Using ChatMessage interface from services/supabase.ts\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ChatInterface({\n  sidebarOpen,\n  setSidebarOpen\n}) {\n  var _DEFAULT_LLM_MODELS$f;\n  const {\n    currentSession,\n    messages,\n    createNewChat,\n    loadChatSession,\n    addMessage,\n    updateMessage,\n    clearCurrentChat\n  } = useChatContext();\n  const [input, setInput] = useState('');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [activeLLMModel, setActiveLLMModel] = useState('gemini-2.0-flash'); // Default LLM model\n  const [showTrainLoader, setShowTrainLoader] = useState(false);\n  const [currentSearchStage, setCurrentSearchStage] = useState('initializing');\n  const messagesEndRef = useRef(null);\n\n  // Scroll to bottom of chat whenever messages change\n  useEffect(() => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({\n        behavior: 'smooth'\n      });\n    }\n  }, [messages]);\n\n  // Helper function to determine if visual content should be shown\n  const shouldShowVisualContent = (userQuestion, documentAnswer) => {\n    const userQuery = userQuestion.toLowerCase();\n\n    // Always show visual content when user asks for images\n    if (userQuery.includes('image')) {\n      return true;\n    }\n\n    // Detect different types of requests\n    const askedForImages = userQuery.includes('image') || userQuery.includes('picture') || userQuery.includes('diagram') || userQuery.includes('chart');\n    const askedForTableData = (userQuery.includes('table') || userQuery.includes('quotation')) && (userQuery.includes('give me') || userQuery.includes('show me table') || userQuery.includes('get') || userQuery.includes('provide'));\n    const askedToShowImages = userQuery.includes('show me image') || userQuery.includes('display image') || userQuery.includes('see image');\n    const answerLower = (documentAnswer === null || documentAnswer === void 0 ? void 0 : documentAnswer.toLowerCase()) || '';\n    const hasTableInAnswer = answerLower.includes('<table>') || answerLower.includes('|') || answerLower.includes('table') && answerLower.length > 200;\n    const hasImageDescription = answerLower.includes('image') || answerLower.includes('figure') || answerLower.includes('diagram');\n    const answerHasContent = answerLower.length > 50;\n\n    // ALWAYS show visual content if user specifically asks to see images/visuals\n    if (askedToShowImages || askedForImages && (userQuery.includes('show') || userQuery.includes('display'))) {\n      return true;\n    }\n\n    // NEVER show if user asked for table data (they want the actual data, not images)\n    if (askedForTableData && !askedForImages) {\n      return false;\n    }\n\n    // For other cases, show only if answer doesn't already provide adequate information\n    const shouldShow = !hasTableInAnswer &&\n    // Don't show if answer already has table data\n    !hasImageDescription && (\n    // Don't show if answer already describes images well\n\n    askedForImages && !answerHasContent ||\n    // Show images if user asked and answer is short\n    !answerHasContent && answerLower.length < 50 // Show for very short answers\n    );\n    return shouldShow;\n  };\n\n  // Helper function to filter visual content types based on user query\n  const filterVisualContent = (source, userQuestion) => {\n    if (typeof source !== 'object') {\n      return false;\n    }\n    const userQueryLower = userQuestion.toLowerCase();\n    const sourceObj = source;\n    console.log('🔍 Filtering visual content:', {\n      filename: sourceObj.filename,\n      content_type: sourceObj.content_type,\n      has_visual_content: !!sourceObj.visual_content,\n      query: userQuestion\n    });\n\n    // If user asked for logo, prioritize images\n    if (userQueryLower.includes('logo')) {\n      const isImage = sourceObj.content_type === 'image';\n      if (isImage) {\n        var _sourceObj$filename, _sourceObj$visual_con, _sourceObj$visual_con2;\n        // For logo queries, be more permissive - show any images that could contain logos\n        const filename = ((_sourceObj$filename = sourceObj.filename) === null || _sourceObj$filename === void 0 ? void 0 : _sourceObj$filename.toLowerCase()) || '';\n        const visualContentStr = JSON.stringify(sourceObj.visual_content || {}).toLowerCase();\n\n        // Check for VASP specifically (since that's what user is asking for)\n        const hasVasp = filename.includes('vasp') || visualContentStr.includes('vasp') || ((_sourceObj$visual_con = sourceObj.visual_content) === null || _sourceObj$visual_con === void 0 ? void 0 : _sourceObj$visual_con.ocr_text) && sourceObj.visual_content.ocr_text.toLowerCase().includes('vasp');\n\n        // Check for any company/logo indicators\n        const hasCompanyKeywords = filename.includes('company') || filename.includes('logo') || filename.includes('brand') || visualContentStr.includes('logo') || visualContentStr.includes('company') || ((_sourceObj$visual_con2 = sourceObj.visual_content) === null || _sourceObj$visual_con2 === void 0 ? void 0 : _sourceObj$visual_con2.is_logo);\n\n        // If no specific VASP match, show any image for logo queries (be permissive)\n        const shouldShow = hasVasp || hasCompanyKeywords || isImage;\n        console.log('🎯 Logo filtering result:', {\n          filename,\n          hasVasp,\n          hasCompanyKeywords,\n          shouldShow,\n          visual_content_keys: sourceObj.visual_content ? Object.keys(sourceObj.visual_content) : []\n        });\n        return shouldShow;\n      }\n      return false; // Not an image for logo query\n    }\n\n    // Extract specific project/quotation numbers from user query\n    const projectMatch = userQueryLower.match(/project\\s*(\\d+)/);\n    const quotationMatch = userQueryLower.match(/quotation\\s*(\\d+)/);\n\n    // Check if the visual content contains relevant information for the specific request\n    const visualContentStr = JSON.stringify(sourceObj.visual_content || {}).toLowerCase();\n\n    // If user asked for a specific project image\n    if (projectMatch && userQueryLower.includes('image')) {\n      var _sourceObj$visual_con3;\n      const projectNumber = projectMatch[1];\n      const isImage = sourceObj.content_type === 'image';\n\n      // Check if this image is related to the specific project\n      const hasProjectInContent = visualContentStr.includes(`project ${projectNumber}`) || visualContentStr.includes(`project${projectNumber}`);\n      const hasProjectInFilename = sourceObj.filename && sourceObj.filename.toLowerCase().includes(`project ${projectNumber}`);\n      const isOnProjectPage = sourceObj.page && Math.abs(sourceObj.page - parseInt(projectNumber)) <= 1; // Allow adjacent pages\n      const hasProjectContext = ((_sourceObj$visual_con3 = sourceObj.visual_content) === null || _sourceObj$visual_con3 === void 0 ? void 0 : _sourceObj$visual_con3.project_context) && sourceObj.visual_content.project_context.includes(`project ${projectNumber}`);\n      const isRelevantProject = hasProjectInContent || hasProjectInFilename || isOnProjectPage || hasProjectContext;\n\n      // If this is an image and it's relevant to the project, show it\n      // If no images are relevant, we'll fall back to showing all images\n      return isImage && isRelevantProject;\n    }\n\n    // If user asked for a specific quotation table\n    if (quotationMatch && userQueryLower.includes('table')) {\n      const quotationNumber = quotationMatch[1];\n      const isTable = sourceObj.content_type === 'table';\n\n      // Check if this table is related to the specific quotation\n      const isRelevantQuotation = visualContentStr.includes(`quotation ${quotationNumber}`) || visualContentStr.includes(`quotation${quotationNumber}`) || visualContentStr.includes(`quote ${quotationNumber}`);\n      return isTable && isRelevantQuotation;\n    }\n\n    // If user specifically asked for images (but no specific project), only show images\n    if (userQueryLower.includes('image') && !userQueryLower.includes('table')) {\n      const isImage = sourceObj.content_type === 'image';\n      return isImage;\n    }\n\n    // If user specifically asked for tables (but no specific quotation), only show tables\n    if (userQueryLower.includes('table') && !userQueryLower.includes('image')) {\n      const isTable = sourceObj.content_type === 'table';\n      return isTable;\n    }\n\n    // Default: show all visual content\n    return true;\n  };\n\n  // Handle command shortcuts in the textbox\n  const handleCommandShortcut = input => {\n    // Check if the input is a command\n    if (input.startsWith('/')) {\n      const command = input.split(' ')[0].toLowerCase();\n\n      // Command: /model <model-name>\n      if (command === '/model') {\n        const modelArg = input.substring(7).trim();\n        const matchedModel = DEFAULT_LLM_MODELS.find(m => m.name.toLowerCase().includes(modelArg.toLowerCase()) || m.id.toLowerCase().includes(modelArg.toLowerCase()));\n        if (matchedModel && matchedModel.enabled) {\n          setActiveLLMModel(matchedModel.id);\n          setInput('');\n          return 'processed';\n        }\n      }\n\n      // Command: /reset or /clear - clear chat history\n      else if (command === '/reset' || command === '/clear') {\n        clearCurrentChat();\n        setInput('');\n        return 'processed';\n      }\n    }\n    return 'not_processed';\n  };\n  const handleSendMessage = async e => {\n    e.preventDefault();\n    if (!input.trim() || isSubmitting) return;\n\n    // Handle command shortcuts like /reset, /model, etc.\n    if (input.startsWith('/')) {\n      const result = handleCommandShortcut(input);\n      if (result === 'processed') {\n        setInput('');\n        return;\n      }\n      // If not processed as a command, continue as a regular message\n    }\n    return await sendUserMessage(input);\n  };\n  const sendUserMessage = async messageText => {\n    var _messagesEndRef$curre;\n    // Create new chat session if none exists\n    if (!currentSession) {\n      await createNewChat();\n    }\n    const userMessage = {\n      id: `user-${Date.now()}`,\n      content: messageText,\n      sender: 'user',\n      timestamp: new Date().toISOString(),\n      chatId: (currentSession === null || currentSession === void 0 ? void 0 : currentSession.id) || 'temp'\n    };\n    addMessage(userMessage);\n    setInput('');\n    const messageId = Date.now();\n    const tempAiMessage = {\n      id: `ai-${messageId}`,\n      content: '',\n      sender: 'ai',\n      loading: true,\n      timestamp: new Date().toISOString(),\n      llm_model: activeLLMModel,\n      chatId: (currentSession === null || currentSession === void 0 ? void 0 : currentSession.id) || 'temp'\n    };\n    addMessage(tempAiMessage);\n    (_messagesEndRef$curre = messagesEndRef.current) === null || _messagesEndRef$curre === void 0 ? void 0 : _messagesEndRef$curre.scrollIntoView({\n      behavior: 'smooth'\n    });\n    setIsSubmitting(true);\n    setShowTrainLoader(true);\n    setCurrentSearchStage('initializing');\n    try {\n      var _messagesEndRef$curre2, _messagesEndRef$curre3;\n      (_messagesEndRef$curre2 = messagesEndRef.current) === null || _messagesEndRef$curre2 === void 0 ? void 0 : _messagesEndRef$curre2.scrollIntoView({\n        behavior: 'smooth'\n      });\n\n      // Simulate search progress updates with train loader\n      setTimeout(() => setCurrentSearchStage('searching_documents'), 500);\n      setTimeout(() => setCurrentSearchStage('searching_websites'), 2000);\n      setTimeout(() => setCurrentSearchStage('generating_answer'), 4000);\n      const response = await sendQuery(messageText, activeLLMModel);\n\n      // Create the AI message based on strict priority logic\n      const aiMessage = {\n        id: `ai-${messageId}`,\n        content: response.answer,\n        document_answer: response.document_answer,\n        website_answer: response.website_answer,\n        llm_model: response.llm_model || activeLLMModel,\n        sender: 'ai',\n        sources: response.sources,\n        document_sources: response.document_sources,\n        website_sources: response.website_sources,\n        chatId: (currentSession === null || currentSession === void 0 ? void 0 : currentSession.id) || 'temp',\n        llm_fallback_used: response.llm_fallback_used // CORRECTED: Use llm_fallback_used instead of llmFallbackUsed\n      };\n      updateMessage(tempAiMessage.id, aiMessage);\n      (_messagesEndRef$curre3 = messagesEndRef.current) === null || _messagesEndRef$curre3 === void 0 ? void 0 : _messagesEndRef$curre3.scrollIntoView({\n        behavior: 'smooth'\n      });\n    } catch (error) {\n      console.error('Error sending message:', error);\n\n      // Provide more helpful error message without the specific query\n      const errorMessage = {\n        id: `ai-${messageId}`,\n        content: `I'm sorry, I encountered an issue while trying to answer your question. This might be due to backend connectivity issues or a problem with processing your request. Please try again or rephrase your question.`,\n        sender: 'ai',\n        chatId: (currentSession === null || currentSession === void 0 ? void 0 : currentSession.id) || 'temp',\n        llm_fallback_used: true // CORRECTED: Use llm_fallback_used instead of llmFallbackUsed\n      };\n      updateMessage(tempAiMessage.id, errorMessage);\n    } finally {\n      setIsSubmitting(false);\n      setShowTrainLoader(false);\n      setCurrentSearchStage('complete');\n    }\n  };\n  const processDocumentSources = sources => {\n    if (!sources || sources.length === 0) return [];\n\n    // Group by filename to avoid repetition\n    const groupedSources = {};\n    sources.forEach(source => {\n      let filename;\n      let page;\n      if (typeof source === 'string') {\n        // Parse string format like \"MaintenanceManual.pdf – Page 3\"\n        const match = source.match(/([^–]+)(?:\\s*–\\s*Page\\s*(\\d+))?/i);\n        filename = match ? match[1].trim() : source;\n        page = match && match[2] ? parseInt(match[2]) : 1;\n      } else {\n        filename = source.name || source.filename || \"Unknown Document\";\n        // NEW: support multi-page list coming from backend\n        if (source.pages && Array.isArray(source.pages) && source.pages.length > 0) {\n          page = source.pages;\n        } else {\n          page = source.page || 1;\n        }\n      }\n      if (!groupedSources[filename]) {\n        groupedSources[filename] = {\n          filename,\n          pages: []\n        };\n      }\n      const addPage = p => {\n        if (!groupedSources[filename].pages.includes(p)) {\n          groupedSources[filename].pages.push(p);\n        }\n      };\n      if (Array.isArray(page)) {\n        page.forEach(addPage);\n      } else {\n        addPage(page);\n      }\n    });\n\n    // Convert to display format with viewer links\n    return Object.values(groupedSources).map(group => {\n      const sortedPages = group.pages.sort((a, b) => a - b);\n      const pageText = sortedPages.length === 1 ? `Page ${sortedPages[0]}` : `Pages ${sortedPages.join(', ')}`;\n\n      // Create link for document viewer that opens at exact page number\n      return {\n        text: `${group.filename} – ${pageText}`,\n        link: `/viewer?file=${encodeURIComponent(group.filename)}&page=${sortedPages[0]}`,\n        isDocument: true\n      };\n    });\n  };\n  const processWebsiteSources = sources => {\n    if (!sources || sources.length === 0) return [];\n\n    // Remove duplicates and format\n    const uniqueUrls = new Set();\n    const processed = [];\n    sources.forEach(source => {\n      let url;\n      let displayText;\n      if (typeof source === 'string') {\n        url = source.startsWith('http') ? source : `https://${source}`;\n        try {\n          const urlObj = new URL(url);\n          displayText = urlObj.hostname.replace(/^www\\./, '');\n        } catch {\n          displayText = source;\n        }\n      } else {\n        url = source.url || 'https://railgpt.indianrailways.gov.in';\n        try {\n          const urlObj = new URL(url);\n          displayText = urlObj.hostname.replace(/^www\\./, '');\n        } catch {\n          displayText = url;\n        }\n      }\n      if (!uniqueUrls.has(url)) {\n        uniqueUrls.add(url);\n        processed.push({\n          text: displayText,\n          link: url,\n          isDocument: false // Mark as website source\n        });\n      }\n    });\n    return processed; // Return all website sources\n  };\n\n  // Component for expandable source list with appropriate click behaviors\n  const SourceList = ({\n    items,\n    maxVisible = 3\n  }) => {\n    const [expanded, setExpanded] = useState(items.length <= maxVisible);\n    if (items.length === 0) return null;\n    const visibleItems = expanded ? items : items.slice(0, maxVisible);\n    const hasMore = items.length > maxVisible;\n    const handleDocumentClick = (e, link) => {\n      // If we want to handle document links in a special way, we can do so here\n      // For example, we could open a modal or new tab with the document viewer\n      // Currently, just allowing regular link behavior\n    };\n    return /*#__PURE__*/_jsxDEV(\"ul\", {\n      className: \"text-xs list-disc pl-4 mt-1 space-y-1\",\n      children: [visibleItems.map((item, index) => /*#__PURE__*/_jsxDEV(\"li\", {\n        children: item.link ? /*#__PURE__*/_jsxDEV(\"a\", {\n          href: item.link,\n          target: \"_blank\",\n          rel: \"noopener noreferrer\",\n          className: \"hover:underline text-blue-600 transition-colors duration-200\",\n          title: item.isDocument ? \"Open document at this page\" : \"Open website in new tab\",\n          onClick: item.isDocument ? e => handleDocumentClick(e, item.link) : undefined,\n          children: item.text\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 468,\n          columnNumber: 15\n        }, this) : /*#__PURE__*/_jsxDEV(\"span\", {\n          className: \"text-gray-700\",\n          children: item.text\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 479,\n          columnNumber: 15\n        }, this)\n      }, index, false, {\n        fileName: _jsxFileName,\n        lineNumber: 466,\n        columnNumber: 15\n      }, this)), hasMore && !expanded && /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"list-none\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setExpanded(true),\n          className: \"text-blue-500 hover:underline text-xs transition-colors duration-200\",\n          children: [\"+ \", items.length - maxVisible, \" more sources\"]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 485,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 484,\n        columnNumber: 11\n      }, this), hasMore && expanded && /*#__PURE__*/_jsxDEV(\"li\", {\n        className: \"list-none\",\n        children: /*#__PURE__*/_jsxDEV(\"button\", {\n          onClick: () => setExpanded(false),\n          className: \"text-blue-500 hover:underline text-xs transition-colors duration-200\",\n          children: \"Show less\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 495,\n          columnNumber: 15\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 494,\n        columnNumber: 11\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 464,\n      columnNumber: 7\n    }, this);\n  };\n\n  // Sidebar handlers\n  const handleChatSelect = async chatSession => {\n    // Load the selected chat session using context\n    await loadChatSession(chatSession.id);\n    setActiveLLMModel(chatSession.model_used || 'gemini-2.0-flash');\n    setSidebarOpen(false); // Close sidebar on mobile after selection\n  };\n  const handleNewChat = async () => {\n    console.log('Creating new chat...');\n    await createNewChat();\n    setSidebarOpen(false); // Close sidebar on mobile after creating new chat\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    className: \"flex h-screen bg-gray-100 transition-colors duration-300\",\n    children: [/*#__PURE__*/_jsxDEV(TrainLoader, {\n      isVisible: showTrainLoader,\n      message: (() => {\n        switch (currentSearchStage) {\n          case 'searching_documents':\n            return \"RailGPT Searching in Documents...\";\n          case 'searching_websites':\n            return \"RailGPT Searching in Websites...\";\n          case 'generating_answer':\n            return \"RailGPT Generating Response...\";\n          default:\n            return \"RailGPT Processing Your Query...\";\n        }\n      })(),\n      trainType: \"express\",\n      currentStage: currentSearchStage,\n      sidebarOpen: sidebarOpen\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 524,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(ChatSidebar, {\n      isOpen: sidebarOpen,\n      onToggle: () => setSidebarOpen(!sidebarOpen),\n      currentChatId: (currentSession === null || currentSession === void 0 ? void 0 : currentSession.id) || '',\n      onChatSelect: handleChatSelect,\n      onNewChat: handleNewChat\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 544,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      className: `flex flex-col flex-1 transition-all duration-300 ${sidebarOpen ? 'lg:ml-80' : ''}`,\n      children: [/*#__PURE__*/_jsxDEV(\"div\", {\n        className: `flex-1 ${messages.length > 0 ? 'overflow-y-auto' : 'overflow-hidden'} p-4 pb-32`,\n        children: messages.length === 0 ? /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"flex items-center justify-center h-full\",\n          children: /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"text-center text-gray-500\",\n            children: [/*#__PURE__*/_jsxDEV(\"p\", {\n              className: \"text-xl font-semibold mb-3\",\n              children: \"Welcome to RailGPT!\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 559,\n              columnNumber: 17\n            }, this), /*#__PURE__*/_jsxDEV(\"p\", {\n              children: \"Ask questions about Indian Railways...\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 560,\n              columnNumber: 17\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 558,\n            columnNumber: 15\n          }, this)\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 557,\n          columnNumber: 13\n        }, this) : /*#__PURE__*/_jsxDEV(\"div\", {\n          children: [messages.map(message => /*#__PURE__*/_jsxDEV(\"div\", {\n            className: `mb-4 ${message.sender === 'user' ? 'flex justify-end' : 'flex justify-start'}`,\n            children: /*#__PURE__*/_jsxDEV(\"div\", {\n              className: `max-w-4xl rounded-lg p-4 transition-colors duration-300 ${message.sender === 'user' ? 'bg-blue-500 text-white' : 'bg-white text-gray-800 shadow-md'}`,\n              children: [/*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"flex justify-between items-start mb-1\",\n                children: [/*#__PURE__*/_jsxDEV(\"span\", {\n                  className: \"font-semibold\",\n                  children: message.sender === 'user' ? 'You' : 'RailGPT'\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 580,\n                  columnNumber: 23\n                }, this), message.timestamp && /*#__PURE__*/_jsxDEV(\"span\", {\n                  className: `text-xs ml-2 ${message.sender === 'user' ? 'text-blue-100' : 'text-gray-500'}`,\n                  children: new Date(message.timestamp).toLocaleTimeString()\n                }, void 0, false, {\n                  fileName: _jsxFileName,\n                  lineNumber: 584,\n                  columnNumber: 25\n                }, this)]\n              }, void 0, true, {\n                fileName: _jsxFileName,\n                lineNumber: 579,\n                columnNumber: 21\n              }, this), message.sender === 'user' && message.content && /*#__PURE__*/_jsxDEV(\"div\", {\n                className: \"mt-2 whitespace-pre-wrap\",\n                children: message.content\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 596,\n                columnNumber: 23\n              }, this), message.sender === 'ai' && /*#__PURE__*/_jsxDEV(\"div\", {\n                children: ((_message$document_ans, _message$website_answ, _message$document_sou, _message$website_sour) => {\n                  // Only hide if this specific message is loading AND has no content yet\n                  if (message.loading && showTrainLoader && !message.content && !message.document_answer && !message.website_answer) {\n                    return null;\n                  }\n\n                  // Process sources with improved deduplication\n                  const documentSourceItems = processDocumentSources(message.document_sources);\n                  const websiteSourceItems = processWebsiteSources(message.website_sources);\n\n                  // STRICT RAILGPT PRIORITY LOGIC: Check what content is available for conditional display\n                  const hasDocumentContent = !!(message.document_answer && message.document_answer.trim() !== \"\");\n                  const hasWebsiteContent = !!(message.website_answer && message.website_answer.trim() !== \"\");\n                  // STRICT: LLM fallback ONLY when NO relevant chunks found (explicit flag or no content)\n                  const hasLLMFallback = message.llm_fallback_used || !hasDocumentContent && !hasWebsiteContent;\n\n                  // Debug logging for rendering\n                  console.log(`🔍 Rendering message ${message.id}:`, {\n                    hasDocumentContent,\n                    hasWebsiteContent,\n                    hasLLMFallback,\n                    documentAnswerLength: ((_message$document_ans = message.document_answer) === null || _message$document_ans === void 0 ? void 0 : _message$document_ans.length) || 0,\n                    websiteAnswerLength: ((_message$website_answ = message.website_answer) === null || _message$website_answ === void 0 ? void 0 : _message$website_answ.length) || 0,\n                    documentSourcesCount: documentSourceItems.length,\n                    websiteSourcesCount: websiteSourceItems.length,\n                    rawDocumentAnswer: message.document_answer ? 'EXISTS' : 'MISSING',\n                    rawWebsiteAnswer: message.website_answer ? 'EXISTS' : 'MISSING',\n                    rawDocumentSources: ((_message$document_sou = message.document_sources) === null || _message$document_sou === void 0 ? void 0 : _message$document_sou.length) || 0,\n                    rawWebsiteSources: ((_message$website_sour = message.website_sources) === null || _message$website_sour === void 0 ? void 0 : _message$website_sour.length) || 0\n                  });\n\n                  // Get the user's question for context - find the most recent user message before this AI message\n                  const currentMessageIndex = messages.findIndex(m => m.id === message.id);\n                  let userQuestion = '';\n\n                  // Look backwards from current AI message to find the most recent user message\n                  for (let i = currentMessageIndex - 1; i >= 0; i--) {\n                    if (messages[i].sender === 'user' && messages[i].content) {\n                      userQuestion = messages[i].content;\n                      break;\n                    }\n                  }\n                  console.log('🔍 DEBUG: Found user question for AI message:', {\n                    aiMessageId: message.id,\n                    userQuestion\n                  });\n\n                  // Conditional display logic based on answer sources\n                  const components = [];\n                  // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                  let answerSource = '';\n\n                  // STRICT RAILGPT PRIORITY SYSTEM: Only show highest priority source available\n\n                  // PRIORITY 1: Document-Only Results (Highest Priority)\n                  if (hasDocumentContent) {\n                    answerSource = 'document_only';\n                    components.push(/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200 shadow-sm transition-colors duration-300\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-semibold text-blue-800 text-sm mb-3 flex items-center\",\n                        children: [\"\\uD83D\\uDCC4 Answer Found in \", documentSourceItems.length > 1 ? 'Documents' : 'Document']\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 660,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(InteractiveAnswer, {\n                        content: message.document_answer || \"\",\n                        query: userQuestion,\n                        model: message.llm_model,\n                        chatId: message.chatId\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 663,\n                        columnNumber: 33\n                      }, this), (_message$document_sou2 => {\n                        const visualSources = ((_message$document_sou2 = message.document_sources) === null || _message$document_sou2 === void 0 ? void 0 : _message$document_sou2.filter(source => typeof source === 'object' && source.visual_content)) || [];\n                        console.log('🔍 Visual sources found:', visualSources.length);\n                        console.log('🔍 Visual sources:', visualSources.map(s => ({\n                          filename: s.filename,\n                          content_type: s.content_type,\n                          has_visual_content: !!s.visual_content\n                        })));\n\n                        // First try to find sources that match the user's specific request\n                        let relevantSources = visualSources.filter(source => filterVisualContent(source, userQuestion));\n                        console.log('🎯 Relevant sources after filtering:', relevantSources.length);\n\n                        // If no specific matches and user asked for images OR logos, show any available images\n                        if (relevantSources.length === 0 && (userQuestion.toLowerCase().includes('image') || userQuestion.toLowerCase().includes('logo'))) {\n                          relevantSources = visualSources.filter(source => source.content_type === 'image');\n                          console.log('🔍 DEBUG: No specific matches, showing all available images for image/logo query:', relevantSources.length);\n                        }\n                        return relevantSources.length > 0 && shouldShowVisualContent(userQuestion, message.document_answer || '') ? /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mt-4\",\n                          children: [/*#__PURE__*/_jsxDEV(\"h5\", {\n                            className: \"text-sm font-semibold text-blue-800 mb-3\",\n                            children: \"\\uD83D\\uDCCA Visual Content:\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 700,\n                            columnNumber: 39\n                          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                            className: \"space-y-3\",\n                            children: relevantSources.map((source, index) => /*#__PURE__*/_jsxDEV(VisualContent, {\n                              source: source\n                            }, index, false, {\n                              fileName: _jsxFileName,\n                              lineNumber: 703,\n                              columnNumber: 43\n                            }, this))\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 701,\n                            columnNumber: 39\n                          }, this)]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 699,\n                          columnNumber: 37\n                        }, this) : null;\n                      })(), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mt-3 pt-3 border-t border-blue-200\",\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-xs text-blue-700 font-semibold mb-1\",\n                          children: \"Sources:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 711,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(SourceList, {\n                          items: documentSourceItems,\n                          maxVisible: 3\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 712,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 710,\n                        columnNumber: 33\n                      }, this)]\n                    }, \"document-priority\", true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 659,\n                      columnNumber: 31\n                    }, this));\n\n                    // PRIORITY 2: Website-Only Results (Medium Priority - Only if no documents)\n                  } else if (hasWebsiteContent) {\n                    answerSource = 'website_only';\n\n                    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                    const websiteLabel = websiteSourceItems.length > 1 ? 'Extracted Websites' : 'Extracted Website';\n                    components.push(/*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mb-4 p-4 bg-green-50 rounded-lg border border-green-200 shadow-sm transition-colors duration-300\",\n                      children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                        className: \"font-semibold text-green-800 text-sm mb-3 flex items-center\",\n                        children: \"\\uD83C\\uDF10 Answer from Extracted Websites\"\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 726,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(InteractiveAnswer, {\n                        content: message.website_answer || \"\",\n                        query: userQuestion,\n                        model: message.llm_model,\n                        chatId: message.chatId\n                      }, void 0, false, {\n                        fileName: _jsxFileName,\n                        lineNumber: 729,\n                        columnNumber: 33\n                      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mt-3 pt-3 border-t border-green-200\",\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-xs text-green-700 font-semibold mb-1\",\n                          children: \"Sources:\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 736,\n                          columnNumber: 35\n                        }, this), /*#__PURE__*/_jsxDEV(SourceList, {\n                          items: websiteSourceItems,\n                          maxVisible: 3\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 737,\n                          columnNumber: 35\n                        }, this)]\n                      }, void 0, true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 735,\n                        columnNumber: 33\n                      }, this)]\n                    }, \"website-priority\", true, {\n                      fileName: _jsxFileName,\n                      lineNumber: 725,\n                      columnNumber: 31\n                    }, this));\n\n                    // Case 4: No answer from either - fallback to LLM\n                  } else if (hasLLMFallback) {\n                    var _message$llm_model, _message$llm_model2, _message$llm_model3, _message$llm_model4, _message$llm_model5, _message$llm_model6;\n                    answerSource = 'llm';\n                    const modelName = message.llm_model || 'Gemini';\n                    const modelLogo = (_message$llm_model = message.llm_model) !== null && _message$llm_model !== void 0 && _message$llm_model.includes('chatgpt') ? '🤖' : (_message$llm_model2 = message.llm_model) !== null && _message$llm_model2 !== void 0 && _message$llm_model2.includes('groq') ? '⚡' : (_message$llm_model3 = message.llm_model) !== null && _message$llm_model3 !== void 0 && _message$llm_model3.includes('deepseek') ? '🔍' : (_message$llm_model4 = message.llm_model) !== null && _message$llm_model4 !== void 0 && _message$llm_model4.includes('qwen') ? '🌐' : (_message$llm_model5 = message.llm_model) !== null && _message$llm_model5 !== void 0 && _message$llm_model5.includes('ollama') ? '🏠' : (_message$llm_model6 = message.llm_model) !== null && _message$llm_model6 !== void 0 && _message$llm_model6.includes('huggingface') ? '🤗' : '🧠';\n\n                    // Only show the LLM fallback card if not in loading state\n                    if (!showTrainLoader || !message.loading) {\n                      components.push(/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mb-4 p-4 bg-purple-50 rounded-lg border border-purple-200 shadow-sm transition-colors duration-300\",\n                        children: [/*#__PURE__*/_jsxDEV(\"h4\", {\n                          className: \"font-semibold text-purple-800 text-sm mb-3 flex items-center\",\n                          children: [modelLogo, \" Answer generated by \", modelName]\n                        }, void 0, true, {\n                          fileName: _jsxFileName,\n                          lineNumber: 758,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(InteractiveAnswer, {\n                          content: message.content || \"I couldn't find any relevant information to answer your question.\",\n                          query: userQuestion,\n                          model: message.llm_model || 'Gemini',\n                          chatId: message.chatId\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 761,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n                          className: \"mt-3 pt-3 border-t border-purple-200\",\n                          children: /*#__PURE__*/_jsxDEV(\"p\", {\n                            className: \"text-xs text-purple-600 italic\",\n                            children: \"This answer was generated by an AI model as no relevant information was found in your documents or websites.\"\n                          }, void 0, false, {\n                            fileName: _jsxFileName,\n                            lineNumber: 768,\n                            columnNumber: 35\n                          }, this)\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 767,\n                          columnNumber: 33\n                        }, this)]\n                      }, \"llm-fallback\", true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 757,\n                        columnNumber: 31\n                      }, this));\n                    }\n\n                    // Case 5: No sources found and fallback disabled (or similar edge case)\n                  } else {\n                    // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                    answerSource = 'no_results';\n\n                    // Only show the \"no results\" card if not in loading state\n                    if (!showTrainLoader || !message.loading) {\n                      components.push(/*#__PURE__*/_jsxDEV(\"div\", {\n                        className: \"mt-3 p-4 bg-gray-50 rounded-lg border border-gray-200 transition-colors duration-300\",\n                        children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                          className: \"text-sm text-gray-600 mb-2\",\n                          children: \"No sources found\"\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 785,\n                          columnNumber: 33\n                        }, this), /*#__PURE__*/_jsxDEV(InteractiveAnswer, {\n                          content: message.content || \"I couldn't process your question. Please try rephrasing or check if documents/websites are uploaded.\",\n                          query: userQuestion,\n                          model: message.llm_model || 'Gemini',\n                          chatId: message.chatId\n                        }, void 0, false, {\n                          fileName: _jsxFileName,\n                          lineNumber: 786,\n                          columnNumber: 33\n                        }, this)]\n                      }, \"no-results\", true, {\n                        fileName: _jsxFileName,\n                        lineNumber: 784,\n                        columnNumber: 31\n                      }, this));\n                    }\n                  }\n\n                  // If we have components to display, render them\n                  if (components.length > 0) {\n                    return /*#__PURE__*/_jsxDEV(\"div\", {\n                      className: \"mt-3\",\n                      children: components\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 800,\n                      columnNumber: 31\n                    }, this);\n                  }\n\n                  // Fallback for any unhandled edge cases (should rarely happen)\n                  console.warn(\"Frontend: Unhandled rendering case\");\n                  return /*#__PURE__*/_jsxDEV(\"div\", {\n                    className: \"mt-3 p-4 bg-gray-50 rounded-lg border border-gray-200 transition-colors duration-300\",\n                    children: [/*#__PURE__*/_jsxDEV(\"p\", {\n                      className: \"text-sm text-gray-600 mb-2\",\n                      children: \"Rendering Error\"\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 810,\n                      columnNumber: 31\n                    }, this), /*#__PURE__*/_jsxDEV(InteractiveAnswer, {\n                      content: message.content || \"An error occurred while rendering the response.\",\n                      query: userQuestion,\n                      model: message.llm_model || 'Gemini',\n                      chatId: message.chatId\n                    }, void 0, false, {\n                      fileName: _jsxFileName,\n                      lineNumber: 811,\n                      columnNumber: 31\n                    }, this)]\n                  }, void 0, true, {\n                    fileName: _jsxFileName,\n                    lineNumber: 809,\n                    columnNumber: 29\n                  }, this);\n                })()\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 601,\n                columnNumber: 23\n              }, this)]\n            }, void 0, true, {\n              fileName: _jsxFileName,\n              lineNumber: 572,\n              columnNumber: 19\n            }, this)\n          }, message.id, false, {\n            fileName: _jsxFileName,\n            lineNumber: 566,\n            columnNumber: 17\n          }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n            ref: messagesEndRef,\n            style: {\n              float: 'left',\n              clear: 'both'\n            }\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 825,\n            columnNumber: 15\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 564,\n          columnNumber: 13\n        }, this)\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 555,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n        className: `border-t border-gray-300 p-4 bg-white fixed bottom-0 right-0 z-20 shadow-lg transition-all duration-300 ${sidebarOpen ? 'lg:left-80 left-0' : 'left-0'}`,\n        children: [/*#__PURE__*/_jsxDEV(\"form\", {\n          onSubmit: handleSendMessage,\n          className: \"flex items-center space-x-2\",\n          children: [/*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex-1 relative flex\",\n            children: /*#__PURE__*/_jsxDEV(\"input\", {\n              type: \"text\",\n              value: input,\n              onChange: e => {\n                const newValue = e.target.value;\n                setInput(newValue);\n                // Don't handle command shortcuts as you type, only on submit\n              },\n              placeholder: \"Type your message... (/model, /reset, /clear)\",\n              className: \"w-full p-2 border border-gray-300 bg-white text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300\",\n              disabled: isSubmitting,\n              \"aria-label\": \"Message input\"\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 834,\n              columnNumber: 15\n            }, this)\n          }, void 0, false, {\n            fileName: _jsxFileName,\n            lineNumber: 833,\n            columnNumber: 13\n          }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n            className: \"flex items-center space-x-2\",\n            children: [/*#__PURE__*/_jsxDEV(LLMSelector, {\n              currentModel: activeLLMModel,\n              onModelChange: modelId => setActiveLLMModel(modelId),\n              isLoading: isSubmitting\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 850,\n              columnNumber: 15\n            }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n              type: \"submit\",\n              disabled: isSubmitting || !input.trim(),\n              className: \"bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 flex items-center justify-center transition-colors duration-300\",\n              title: \"Send message\",\n              children: /*#__PURE__*/_jsxDEV(\"span\", {\n                children: isSubmitting ? \"Sending...\" : \"Send\"\n              }, void 0, false, {\n                fileName: _jsxFileName,\n                lineNumber: 861,\n                columnNumber: 17\n              }, this)\n            }, void 0, false, {\n              fileName: _jsxFileName,\n              lineNumber: 855,\n              columnNumber: 15\n            }, this)]\n          }, void 0, true, {\n            fileName: _jsxFileName,\n            lineNumber: 849,\n            columnNumber: 13\n          }, this)]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 832,\n          columnNumber: 11\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          className: \"text-xs text-gray-400 mt-1 text-center\",\n          children: [\"Current model: \", ((_DEFAULT_LLM_MODELS$f = DEFAULT_LLM_MODELS.find(m => m.id === activeLLMModel)) === null || _DEFAULT_LLM_MODELS$f === void 0 ? void 0 : _DEFAULT_LLM_MODELS$f.name) || activeLLMModel]\n        }, void 0, true, {\n          fileName: _jsxFileName,\n          lineNumber: 865,\n          columnNumber: 23\n        }, this)]\n      }, void 0, true, {\n        fileName: _jsxFileName,\n        lineNumber: 831,\n        columnNumber: 9\n      }, this), messages.length > 0 && /*#__PURE__*/_jsxDEV(\"div\", {\n        className: \"h-36\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 870,\n        columnNumber: 33\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 553,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 522,\n    columnNumber: 5\n  }, this);\n}\nfunction App({\n  sidebarOpen,\n  setSidebarOpen\n}) {\n  return /*#__PURE__*/_jsxDEV(ChatInterface, {\n    sidebarOpen: sidebarOpen,\n    setSidebarOpen: setSidebarOpen\n  }, void 0, false, {\n    fileName: _jsxFileName,\n    lineNumber: 883,\n    columnNumber: 5\n  }, this);\n}\nexport default App;", "map": {"version": 3, "names": ["React", "useState", "useRef", "useEffect", "<PERSON><PERSON><PERSON><PERSON>", "LLMSelector", "DEFAULT_LLM_MODELS", "InteractiveAnswer", "ChatSidebar", "TrainLoader", "VisualContent", "useChatContext", "jsxDEV", "_jsxDEV", "ChatInterface", "sidebarOpen", "setSidebarOpen", "_DEFAULT_LLM_MODELS$f", "currentSession", "messages", "createNewChat", "loadChatSession", "addMessage", "updateMessage", "clearCurrentChat", "input", "setInput", "isSubmitting", "setIsSubmitting", "activeLLMModel", "setActiveLLMModel", "showTrainLoader", "setShowTrainLoader", "currentSearchStage", "setCurrentSearchStage", "messagesEndRef", "current", "scrollIntoView", "behavior", "shouldShowVisualContent", "userQuestion", "documentAnswer", "userQuery", "toLowerCase", "includes", "askedForImages", "askedForTableData", "askedToShowImages", "answerLower", "hasTableInAnswer", "length", "hasImageDescription", "answerHasContent", "shouldShow", "filterVisualContent", "source", "userQueryLower", "sourceObj", "console", "log", "filename", "content_type", "has_visual_content", "visual_content", "query", "isImage", "_sourceObj$filename", "_sourceObj$visual_con", "_sourceObj$visual_con2", "visualContentStr", "JSON", "stringify", "has<PERSON>asp", "ocr_text", "hasCompanyKeywords", "is_logo", "visual_content_keys", "Object", "keys", "projectMatch", "match", "quotationMatch", "_sourceObj$visual_con3", "projectNumber", "hasProjectInContent", "hasProjectInFilename", "isOnProjectPage", "page", "Math", "abs", "parseInt", "hasProjectContext", "project_context", "isRelevantProject", "quotationNumber", "isTable", "isRelevantQuotation", "handleCommandShortcut", "startsWith", "command", "split", "modelArg", "substring", "trim", "matchedModel", "find", "m", "name", "id", "enabled", "handleSendMessage", "e", "preventDefault", "result", "sendUserMessage", "messageText", "_messagesEndRef$curre", "userMessage", "Date", "now", "content", "sender", "timestamp", "toISOString", "chatId", "messageId", "tempAiMessage", "loading", "llm_model", "_messagesEndRef$curre2", "_messagesEndRef$curre3", "setTimeout", "response", "aiMessage", "answer", "document_answer", "website_answer", "sources", "document_sources", "website_sources", "llm_fallback_used", "error", "errorMessage", "processDocumentSources", "groupedSources", "for<PERSON>ach", "pages", "Array", "isArray", "addPage", "p", "push", "values", "map", "group", "sortedPages", "sort", "a", "b", "pageText", "join", "text", "link", "encodeURIComponent", "isDocument", "processWebsiteSources", "uniqueUrls", "Set", "processed", "url", "displayText", "url<PERSON>bj", "URL", "hostname", "replace", "has", "add", "SourceList", "items", "maxVisible", "expanded", "setExpanded", "visibleItems", "slice", "hasMore", "handleDocumentClick", "className", "children", "item", "index", "href", "target", "rel", "title", "onClick", "undefined", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "handleChatSelect", "chatSession", "model_used", "handleNewChat", "isVisible", "message", "trainType", "currentStage", "isOpen", "onToggle", "currentChatId", "onChatSelect", "onNewChat", "toLocaleTimeString", "_message$document_ans", "_message$website_answ", "_message$document_sou", "_message$website_sour", "documentSourceItems", "websiteSourceItems", "hasDocumentContent", "has<PERSON>ebsite<PERSON><PERSON>nt", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "documentAnswerLength", "websiteAnswerLength", "documentSourcesCount", "websiteSourcesCount", "rawDocumentAnswer", "rawWebsiteAnswer", "rawDocumentSources", "rawWebsiteSources", "currentMessageIndex", "findIndex", "i", "aiMessageId", "components", "answerSource", "model", "_message$document_sou2", "visualSources", "filter", "s", "relevantSources", "websiteLabel", "_message$llm_model", "_message$llm_model2", "_message$llm_model3", "_message$llm_model4", "_message$llm_model5", "_message$llm_model6", "modelName", "modelLogo", "warn", "ref", "style", "float", "clear", "onSubmit", "type", "value", "onChange", "newValue", "placeholder", "disabled", "currentModel", "onModelChange", "modelId", "isLoading", "App"], "sources": ["C:/IR App/frontend/src/App.tsx"], "sourcesContent": ["import React, { useState, useRef, useEffect } from 'react';\nimport './App.css';\nimport { sendQuery } from './services/api';\nimport LLMSelector, { DEFAULT_LLM_MODELS } from './components/ui/LLMSelector';\nimport InteractiveAnswer from './components/ui/InteractiveAnswer';\nimport ChatSidebar from './components/chat/ChatSidebar';\nimport TrainLoader from './components/ui/TrainLoader';\nimport VisualContent from './components/ui/VisualContent';\nimport { useChatContext } from './contexts/ChatContext';\nimport { ChatSession, ChatMessage } from './services/supabase';\n\ninterface Source {\n  source_type: string;\n  filename?: string;\n  page?: number;\n  pages?: number[];\n  url?: string;\n  link?: string; // For document viewer links\n  name?: string; // For display name\n  // Visual content fields\n  content_type?: string;  // \"text\", \"table\", \"image\", \"chart_diagram\"\n  visual_content?: Record<string, any>;  // Visual content metadata\n  storage_url?: string;  // URL for stored visual content\n  display_type?: string;  // \"text\", \"html_table\", \"image\", \"base64_image\"\n}\n\n// Using ChatMessage interface from services/supabase.ts\n\ninterface ChatInterfaceProps {\n  sidebarOpen: boolean;\n  setSidebarOpen: React.Dispatch<React.SetStateAction<boolean>>;\n}\n\nfunction ChatInterface({ sidebarOpen, setSidebarOpen }: ChatInterfaceProps) {\n  const {\n    currentSession,\n    messages,\n    createNewChat,\n    loadChatSession,\n    addMessage,\n    updateMessage,\n    clearCurrentChat\n  } = useChatContext();\n\n  const [input, setInput] = useState<string>('');\n  const [isSubmitting, setIsSubmitting] = useState(false);\n  const [activeLLMModel, setActiveLLMModel] = useState('gemini-2.0-flash'); // Default LLM model\n  const [showTrainLoader, setShowTrainLoader] = useState(false);\n  const [currentSearchStage, setCurrentSearchStage] = useState<'initializing' | 'searching_documents' | 'searching_websites' | 'generating_answer' | 'complete'>('initializing');\n  const messagesEndRef = useRef<HTMLDivElement>(null);\n\n  // Scroll to bottom of chat whenever messages change\n  useEffect(() => {\n    if (messagesEndRef.current) {\n      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });\n    }\n  }, [messages]);\n\n  // Helper function to determine if visual content should be shown\n  const shouldShowVisualContent = (userQuestion: string, documentAnswer: string): boolean => {\n    const userQuery = userQuestion.toLowerCase();\n\n    // Always show visual content when user asks for images\n    if (userQuery.includes('image')) {\n      return true;\n    }\n    \n    // Detect different types of requests\n    const askedForImages = userQuery.includes('image') || userQuery.includes('picture') || userQuery.includes('diagram') || userQuery.includes('chart');\n    const askedForTableData = (userQuery.includes('table') || userQuery.includes('quotation')) && \n                             (userQuery.includes('give me') || userQuery.includes('show me table') || userQuery.includes('get') || userQuery.includes('provide'));\n    const askedToShowImages = userQuery.includes('show me image') || userQuery.includes('display image') || userQuery.includes('see image');\n    \n    const answerLower = documentAnswer?.toLowerCase() || '';\n    const hasTableInAnswer = answerLower.includes('<table>') || answerLower.includes('|') || \n                            (answerLower.includes('table') && answerLower.length > 200);\n    const hasImageDescription = answerLower.includes('image') || answerLower.includes('figure') || answerLower.includes('diagram');\n    const answerHasContent = answerLower.length > 50;\n    \n    // ALWAYS show visual content if user specifically asks to see images/visuals\n    if (askedToShowImages || (askedForImages && (userQuery.includes('show') || userQuery.includes('display')))) {\n      return true;\n    }\n\n    // NEVER show if user asked for table data (they want the actual data, not images)\n    if (askedForTableData && !askedForImages) {\n      return false;\n    }\n    \n    // For other cases, show only if answer doesn't already provide adequate information\n    const shouldShow = !hasTableInAnswer && // Don't show if answer already has table data\n                      !hasImageDescription && // Don't show if answer already describes images well\n                      (\n                        (askedForImages && !answerHasContent) || // Show images if user asked and answer is short\n                        (!answerHasContent && answerLower.length < 50) // Show for very short answers\n                      );\n    \n    return shouldShow;\n  };\n\n  // Helper function to filter visual content types based on user query\n  const filterVisualContent = (source: Source | string, userQuestion: string) => {\n    if (typeof source !== 'object') {\n      return false;\n    }\n\n    const userQueryLower = userQuestion.toLowerCase();\n    const sourceObj = source as Source;\n    \n    console.log('🔍 Filtering visual content:', {\n      filename: sourceObj.filename,\n      content_type: sourceObj.content_type,\n      has_visual_content: !!sourceObj.visual_content,\n      query: userQuestion\n    });\n    \n    // If user asked for logo, prioritize images\n    if (userQueryLower.includes('logo')) {\n      const isImage = sourceObj.content_type === 'image';\n      \n      if (isImage) {\n        // For logo queries, be more permissive - show any images that could contain logos\n        const filename = sourceObj.filename?.toLowerCase() || '';\n        const visualContentStr = JSON.stringify(sourceObj.visual_content || {}).toLowerCase();\n        \n        // Check for VASP specifically (since that's what user is asking for)\n        const hasVasp = filename.includes('vasp') || \n                       visualContentStr.includes('vasp') ||\n                       (sourceObj.visual_content?.ocr_text && \n                        sourceObj.visual_content.ocr_text.toLowerCase().includes('vasp'));\n        \n        // Check for any company/logo indicators\n        const hasCompanyKeywords = filename.includes('company') || \n                                  filename.includes('logo') ||\n                                  filename.includes('brand') ||\n                                  visualContentStr.includes('logo') ||\n                                  visualContentStr.includes('company') ||\n                                  sourceObj.visual_content?.is_logo;\n        \n        // If no specific VASP match, show any image for logo queries (be permissive)\n        const shouldShow = hasVasp || hasCompanyKeywords || isImage;\n        \n        console.log('🎯 Logo filtering result:', {\n          filename,\n          hasVasp,\n          hasCompanyKeywords,\n          shouldShow,\n          visual_content_keys: sourceObj.visual_content ? Object.keys(sourceObj.visual_content) : []\n        });\n        \n        return shouldShow;\n      }\n      \n      return false; // Not an image for logo query\n    }\n    \n    // Extract specific project/quotation numbers from user query\n    const projectMatch = userQueryLower.match(/project\\s*(\\d+)/);\n    const quotationMatch = userQueryLower.match(/quotation\\s*(\\d+)/);\n    \n    // Check if the visual content contains relevant information for the specific request\n    const visualContentStr = JSON.stringify(sourceObj.visual_content || {}).toLowerCase();\n\n    // If user asked for a specific project image\n    if (projectMatch && userQueryLower.includes('image')) {\n      const projectNumber = projectMatch[1];\n      const isImage = sourceObj.content_type === 'image';\n      \n      // Check if this image is related to the specific project\n      const hasProjectInContent = visualContentStr.includes(`project ${projectNumber}`) || \n                                  visualContentStr.includes(`project${projectNumber}`);\n      const hasProjectInFilename = sourceObj.filename && sourceObj.filename.toLowerCase().includes(`project ${projectNumber}`);\n      const isOnProjectPage = sourceObj.page && Math.abs(sourceObj.page - parseInt(projectNumber)) <= 1; // Allow adjacent pages\n      const hasProjectContext = sourceObj.visual_content?.project_context && \n                               sourceObj.visual_content.project_context.includes(`project ${projectNumber}`);\n      \n      const isRelevantProject = hasProjectInContent || hasProjectInFilename || isOnProjectPage || hasProjectContext;\n      \n      // If this is an image and it's relevant to the project, show it\n      // If no images are relevant, we'll fall back to showing all images\n      return isImage && isRelevantProject;\n    }\n    \n    // If user asked for a specific quotation table\n    if (quotationMatch && userQueryLower.includes('table')) {\n      const quotationNumber = quotationMatch[1];\n      const isTable = sourceObj.content_type === 'table';\n      \n      // Check if this table is related to the specific quotation\n      const isRelevantQuotation = visualContentStr.includes(`quotation ${quotationNumber}`) || \n                                  visualContentStr.includes(`quotation${quotationNumber}`) ||\n                                  visualContentStr.includes(`quote ${quotationNumber}`);\n      \n      return isTable && isRelevantQuotation;\n    }\n    \n    // If user specifically asked for images (but no specific project), only show images\n    if (userQueryLower.includes('image') && !userQueryLower.includes('table')) {\n      const isImage = sourceObj.content_type === 'image';\n      return isImage;\n    }\n\n    // If user specifically asked for tables (but no specific quotation), only show tables\n    if (userQueryLower.includes('table') && !userQueryLower.includes('image')) {\n      const isTable = sourceObj.content_type === 'table';\n      return isTable;\n    }\n\n    // Default: show all visual content\n    return true;\n  };\n\n  // Handle command shortcuts in the textbox\n  const handleCommandShortcut = (input: string) => {\n    // Check if the input is a command\n    if (input.startsWith('/')) {\n      const command = input.split(' ')[0].toLowerCase();\n\n      // Command: /model <model-name>\n      if (command === '/model') {\n        const modelArg = input.substring(7).trim();\n        const matchedModel = DEFAULT_LLM_MODELS.find(m =>\n          m.name.toLowerCase().includes(modelArg.toLowerCase()) ||\n          m.id.toLowerCase().includes(modelArg.toLowerCase())\n        );\n\n        if (matchedModel && matchedModel.enabled) {\n          setActiveLLMModel(matchedModel.id);\n          setInput('');\n          return 'processed';\n        }\n      }\n\n      // Command: /reset or /clear - clear chat history\n      else if (command === '/reset' || command === '/clear') {\n        clearCurrentChat();\n        setInput('');\n        return 'processed';\n      }\n    }\n\n    return 'not_processed';\n  };\n\n  const handleSendMessage = async (e: React.FormEvent) => {\n    e.preventDefault();\n    if (!input.trim() || isSubmitting) return;\n\n    // Handle command shortcuts like /reset, /model, etc.\n    if (input.startsWith('/')) {\n      const result = handleCommandShortcut(input);\n      if (result === 'processed') {\n        setInput('');\n        return;\n      }\n      // If not processed as a command, continue as a regular message\n    }\n\n    return await sendUserMessage(input);\n  };\n\n  const sendUserMessage = async (messageText: string) => {\n    // Create new chat session if none exists\n    if (!currentSession) {\n      await createNewChat();\n    }\n\n    const userMessage: ChatMessage = {\n      id: `user-${Date.now()}`,\n      content: messageText,\n      sender: 'user',\n      timestamp: new Date().toISOString(),\n      chatId: currentSession?.id || 'temp',\n    };\n\n    addMessage(userMessage);\n    setInput('');\n\n    const messageId = Date.now();\n    const tempAiMessage: ChatMessage = {\n      id: `ai-${messageId}`,\n      content: '',\n      sender: 'ai',\n      loading: true,\n      timestamp: new Date().toISOString(),\n      llm_model: activeLLMModel,\n      chatId: currentSession?.id || 'temp',\n    };\n\n    addMessage(tempAiMessage);\n    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n\n    setIsSubmitting(true);\n    setShowTrainLoader(true);\n    setCurrentSearchStage('initializing');\n\n    try {\n      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n\n      // Simulate search progress updates with train loader\n      setTimeout(() => setCurrentSearchStage('searching_documents'), 500);\n      setTimeout(() => setCurrentSearchStage('searching_websites'), 2000);\n      setTimeout(() => setCurrentSearchStage('generating_answer'), 4000);\n\n      const response = await sendQuery(messageText, activeLLMModel);\n\n      // Create the AI message based on strict priority logic\n      const aiMessage: ChatMessage = {\n        id: `ai-${messageId}`,\n        content: response.answer,\n        document_answer: response.document_answer,\n        website_answer: response.website_answer,\n        llm_model: response.llm_model || activeLLMModel,\n        sender: 'ai',\n        sources: response.sources,\n        document_sources: response.document_sources,\n        website_sources: response.website_sources,\n        chatId: currentSession?.id || 'temp',\n        llm_fallback_used: response.llm_fallback_used,  // CORRECTED: Use llm_fallback_used instead of llmFallbackUsed\n      };\n\n      updateMessage(tempAiMessage.id, aiMessage);\n\n      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });\n    } catch (error) {\n      console.error('Error sending message:', error);\n\n      // Provide more helpful error message without the specific query\n      const errorMessage: ChatMessage = {\n        id: `ai-${messageId}`,\n        content: `I'm sorry, I encountered an issue while trying to answer your question. This might be due to backend connectivity issues or a problem with processing your request. Please try again or rephrase your question.`,\n        sender: 'ai',\n        chatId: currentSession?.id || 'temp',\n        llm_fallback_used: true,  // CORRECTED: Use llm_fallback_used instead of llmFallbackUsed\n      };\n\n      updateMessage(tempAiMessage.id, errorMessage);\n    } finally {\n      setIsSubmitting(false);\n      setShowTrainLoader(false);\n      setCurrentSearchStage('complete');\n    }\n  };\n\n  const processDocumentSources = (sources?: Array<Source | string>) => {\n    if (!sources || sources.length === 0) return [];\n\n    // Group by filename to avoid repetition\n    const groupedSources: { [key: string]: { filename: string; pages: number[] } } = {};\n\n    sources.forEach(source => {\n      let filename: string;\n      let page: number | number[];\n\n      if (typeof source === 'string') {\n        // Parse string format like \"MaintenanceManual.pdf – Page 3\"\n        const match = source.match(/([^–]+)(?:\\s*–\\s*Page\\s*(\\d+))?/i);\n        filename = match ? match[1].trim() : source;\n        page = match && match[2] ? parseInt(match[2]) : 1;\n      } else {\n        filename = source.name || source.filename || \"Unknown Document\";\n        // NEW: support multi-page list coming from backend\n        if ((source as any).pages && Array.isArray((source as any).pages) && (source as any).pages.length > 0) {\n          page = (source as any).pages as number[];\n        } else {\n          page = source.page || 1;\n        }\n      }\n\n      if (!groupedSources[filename]) {\n        groupedSources[filename] = { filename, pages: [] };\n      }\n\n      const addPage = (p: number) => {\n        if (!groupedSources[filename].pages.includes(p)) {\n          groupedSources[filename].pages.push(p);\n        }\n      };\n\n      if (Array.isArray(page)) {\n        page.forEach(addPage);\n      } else {\n        addPage(page as number);\n      }\n    });\n\n    // Convert to display format with viewer links\n    return Object.values(groupedSources).map(group => {\n      const sortedPages = group.pages.sort((a, b) => a - b);\n      const pageText = sortedPages.length === 1\n        ? `Page ${sortedPages[0]}`\n        : `Pages ${sortedPages.join(', ')}`;\n\n      // Create link for document viewer that opens at exact page number\n      return {\n        text: `${group.filename} – ${pageText}`,\n        link: `/viewer?file=${encodeURIComponent(group.filename)}&page=${sortedPages[0]}`,\n        isDocument: true\n      };\n    });\n  };\n\n  const processWebsiteSources = (sources?: Array<Source | string>) => {\n    if (!sources || sources.length === 0) return [];\n\n    // Remove duplicates and format\n    const uniqueUrls = new Set<string>();\n    const processed: Array<{ text: string; link: string; isDocument?: boolean }> = [];\n\n    sources.forEach(source => {\n      let url: string;\n      let displayText: string;\n\n      if (typeof source === 'string') {\n        url = source.startsWith('http') ? source : `https://${source}`;\n          try {\n          const urlObj = new URL(url);\n          displayText = urlObj.hostname.replace(/^www\\./, '');\n          } catch {\n          displayText = source;\n        }\n      } else {\n        url = source.url || 'https://railgpt.indianrailways.gov.in';\n        try {\n          const urlObj = new URL(url);\n          displayText = urlObj.hostname.replace(/^www\\./, '');\n        } catch {\n          displayText = url;\n        }\n      }\n\n      if (!uniqueUrls.has(url)) {\n        uniqueUrls.add(url);\n        processed.push({\n          text: displayText,\n          link: url,\n          isDocument: false  // Mark as website source\n        });\n        }\n    });\n\n    return processed; // Return all website sources\n  };\n\n  // Component for expandable source list with appropriate click behaviors\n  const SourceList = ({ items, maxVisible = 3 }: {\n    items: Array<{ text: string; link?: string; isDocument?: boolean }>;\n    maxVisible?: number\n  }) => {\n    const [expanded, setExpanded] = useState(items.length <= maxVisible);\n\n    if (items.length === 0) return null;\n\n    const visibleItems = expanded ? items : items.slice(0, maxVisible);\n    const hasMore = items.length > maxVisible;\n\n    const handleDocumentClick = (e: React.MouseEvent<HTMLAnchorElement>, link: string) => {\n      // If we want to handle document links in a special way, we can do so here\n      // For example, we could open a modal or new tab with the document viewer\n      // Currently, just allowing regular link behavior\n    };\n\n    return (\n      <ul className=\"text-xs list-disc pl-4 mt-1 space-y-1\">\n        {visibleItems.map((item, index) => (\n              <li key={index}>\n                {item.link ? (\n              <a\n                href={item.link}\n                target=\"_blank\"\n                rel=\"noopener noreferrer\"\n                className=\"hover:underline text-blue-600 transition-colors duration-200\"\n                title={item.isDocument ? \"Open document at this page\" : \"Open website in new tab\"}\n                onClick={item.isDocument ? (e) => handleDocumentClick(e, item.link!) : undefined}\n              >\n                    {item.text}\n                  </a>\n                ) : (\n              <span className=\"text-gray-700\">{item.text}</span>\n                )}\n              </li>\n            ))}\n        {hasMore && !expanded && (\n          <li className=\"list-none\">\n            <button\n              onClick={() => setExpanded(true)}\n              className=\"text-blue-500 hover:underline text-xs transition-colors duration-200\"\n            >\n              + {items.length - maxVisible} more sources\n            </button>\n          </li>\n        )}\n        {hasMore && expanded && (\n          <li className=\"list-none\">\n              <button\n                onClick={() => setExpanded(false)}\n              className=\"text-blue-500 hover:underline text-xs transition-colors duration-200\"\n              >\n                Show less\n              </button>\n            </li>\n                )}\n      </ul>\n    );\n  };\n\n  // Sidebar handlers\n  const handleChatSelect = async (chatSession: ChatSession) => {\n    // Load the selected chat session using context\n    await loadChatSession(chatSession.id);\n    setActiveLLMModel(chatSession.model_used || 'gemini-2.0-flash');\n    setSidebarOpen(false); // Close sidebar on mobile after selection\n  };\n\n  const handleNewChat = async () => {\n    console.log('Creating new chat...');\n    await createNewChat();\n    setSidebarOpen(false); // Close sidebar on mobile after creating new chat\n  };\n\n  return (\n    <div className=\"flex h-screen bg-gray-100 transition-colors duration-300\">\n      {/* Train Loader Overlay */}\n      <TrainLoader\n        isVisible={showTrainLoader}\n        message={(() => {\n          switch (currentSearchStage) {\n            case 'searching_documents':\n              return \"RailGPT Searching in Documents...\";\n            case 'searching_websites':\n              return \"RailGPT Searching in Websites...\";\n            case 'generating_answer':\n              return \"RailGPT Generating Response...\";\n            default:\n              return \"RailGPT Processing Your Query...\";\n          }\n        })()}\n        trainType=\"express\"\n        currentStage={currentSearchStage}\n        sidebarOpen={sidebarOpen}\n      />\n\n      {/* Chat Sidebar */}\n      <ChatSidebar\n        isOpen={sidebarOpen}\n        onToggle={() => setSidebarOpen(!sidebarOpen)}\n        currentChatId={currentSession?.id || ''}\n        onChatSelect={handleChatSelect}\n        onNewChat={handleNewChat}\n      />\n\n      {/* Main Chat Area */}\n      <div className={`flex flex-col flex-1 transition-all duration-300 ${sidebarOpen ? 'lg:ml-80' : ''}`}>\n        {/* Message Area - only scrollable when messages exist */}\n        <div className={`flex-1 ${messages.length > 0 ? 'overflow-y-auto' : 'overflow-hidden'} p-4 pb-32`}>\n          {messages.length === 0 ? (\n            <div className=\"flex items-center justify-center h-full\">\n              <div className=\"text-center text-gray-500\">\n                <p className=\"text-xl font-semibold mb-3\">Welcome to RailGPT!</p>\n                <p>Ask questions about Indian Railways...</p>\n              </div>\n            </div>\n          ) : (\n            <div>\n              {messages.map((message) => (\n                <div\n                  key={message.id}\n                  className={`mb-4 ${\n                    message.sender === 'user' ? 'flex justify-end' : 'flex justify-start'\n                  }`}\n                >\n                  <div\n                    className={`max-w-4xl rounded-lg p-4 transition-colors duration-300 ${\n                      message.sender === 'user'\n                        ? 'bg-blue-500 text-white'\n                        : 'bg-white text-gray-800 shadow-md'\n                    }`}\n                  >\n                    <div className=\"flex justify-between items-start mb-1\">\n                      <span className=\"font-semibold\">\n                        {message.sender === 'user' ? 'You' : 'RailGPT'}\n                      </span>\n                      {message.timestamp && (\n                        <span className={`text-xs ml-2 ${\n                          message.sender === 'user'\n                            ? 'text-blue-100'\n                            : 'text-gray-500'\n                        }`}>\n                          {new Date(message.timestamp).toLocaleTimeString()}\n                        </span>\n                      )}\n                    </div>\n\n                    {/* Only show content directly for user messages */}\n                    {message.sender === 'user' && message.content && (\n                      <div className=\"mt-2 whitespace-pre-wrap\">{message.content}</div>\n                    )}\n\n                    {/* AI messages with strict priority display logic */}\n                    {message.sender === 'ai' && (\n                      <div>\n                        {(() => {\n                          // Only hide if this specific message is loading AND has no content yet\n                          if (message.loading && showTrainLoader && !message.content && !message.document_answer && !message.website_answer) {\n                            return null;\n                          }\n\n                          // Process sources with improved deduplication\n                          const documentSourceItems = processDocumentSources(message.document_sources);\n                          const websiteSourceItems = processWebsiteSources(message.website_sources);\n\n                          // STRICT RAILGPT PRIORITY LOGIC: Check what content is available for conditional display\n                          const hasDocumentContent = !!(message.document_answer && message.document_answer.trim() !== \"\");\n                          const hasWebsiteContent = !!(message.website_answer && message.website_answer.trim() !== \"\");\n                          // STRICT: LLM fallback ONLY when NO relevant chunks found (explicit flag or no content)\n                          const hasLLMFallback = message.llm_fallback_used || (!hasDocumentContent && !hasWebsiteContent);\n\n                          // Debug logging for rendering\n                          console.log(`🔍 Rendering message ${message.id}:`, {\n                            hasDocumentContent,\n                            hasWebsiteContent,\n                            hasLLMFallback,\n                            documentAnswerLength: message.document_answer?.length || 0,\n                            websiteAnswerLength: message.website_answer?.length || 0,\n                            documentSourcesCount: documentSourceItems.length,\n                            websiteSourcesCount: websiteSourceItems.length,\n                            rawDocumentAnswer: message.document_answer ? 'EXISTS' : 'MISSING',\n                            rawWebsiteAnswer: message.website_answer ? 'EXISTS' : 'MISSING',\n                            rawDocumentSources: message.document_sources?.length || 0,\n                            rawWebsiteSources: message.website_sources?.length || 0\n                          });\n\n                          // Get the user's question for context - find the most recent user message before this AI message\n                          const currentMessageIndex = messages.findIndex(m => m.id === message.id);\n                          let userQuestion = '';\n                          \n                          // Look backwards from current AI message to find the most recent user message\n                          for (let i = currentMessageIndex - 1; i >= 0; i--) {\n                            if (messages[i].sender === 'user' && messages[i].content) {\n                              userQuestion = messages[i].content;\n                              break;\n                            }\n                          }\n                          \n                          console.log('🔍 DEBUG: Found user question for AI message:', { aiMessageId: message.id, userQuestion });\n\n                          // Conditional display logic based on answer sources\n                          const components = [];\n                          // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                          let answerSource = '';\n\n                          // STRICT RAILGPT PRIORITY SYSTEM: Only show highest priority source available\n\n                          // PRIORITY 1: Document-Only Results (Highest Priority)\n                          if (hasDocumentContent) {\n                            answerSource = 'document_only';\n\n                            components.push(\n                              <div key=\"document-priority\" className=\"mb-4 p-4 bg-blue-50 rounded-lg border border-blue-200 shadow-sm transition-colors duration-300\">\n                                <h4 className=\"font-semibold text-blue-800 text-sm mb-3 flex items-center\">\n                                  📄 Answer Found in {documentSourceItems.length > 1 ? 'Documents' : 'Document'}\n                                </h4>\n                                <InteractiveAnswer\n                                  content={message.document_answer || \"\"}\n                                  query={userQuestion}\n                                  model={message.llm_model}\n                                  chatId={message.chatId}\n                                />\n                                \n                                {/* Display Visual Content with Smart Fallback */}\n                                {(() => {\n                                  const visualSources = message.document_sources?.filter(source => \n                                    typeof source === 'object' && source.visual_content\n                                  ) || [];\n                                  \n                                  console.log('🔍 Visual sources found:', visualSources.length);\n                                  console.log('🔍 Visual sources:', visualSources.map(s => ({\n                                    filename: (s as Source).filename,\n                                    content_type: (s as Source).content_type,\n                                    has_visual_content: !!(s as Source).visual_content\n                                  })));\n                                  \n                                  // First try to find sources that match the user's specific request\n                                  let relevantSources = visualSources.filter(source => \n                                    filterVisualContent(source as Source, userQuestion)\n                                  );\n                                  \n                                  console.log('🎯 Relevant sources after filtering:', relevantSources.length);\n                                  \n                                  // If no specific matches and user asked for images OR logos, show any available images\n                                  if (relevantSources.length === 0 && (userQuestion.toLowerCase().includes('image') || userQuestion.toLowerCase().includes('logo'))) {\n                                    relevantSources = visualSources.filter(source => \n                                      (source as Source).content_type === 'image'\n                                    );\n                                    console.log('🔍 DEBUG: No specific matches, showing all available images for image/logo query:', relevantSources.length);\n                                  }\n                                  \n                                  return relevantSources.length > 0 && shouldShowVisualContent(userQuestion, message.document_answer || '') ? (\n                                    <div className=\"mt-4\">\n                                      <h5 className=\"text-sm font-semibold text-blue-800 mb-3\">📊 Visual Content:</h5>\n                                      <div className=\"space-y-3\">\n                                        {relevantSources.map((source, index) => (\n                                          <VisualContent key={index} source={source as Source} />\n                                        ))}\n                                      </div>\n                                    </div>\n                                  ) : null;\n                                })()}\n                                \n                                <div className=\"mt-3 pt-3 border-t border-blue-200\">\n                                  <p className=\"text-xs text-blue-700 font-semibold mb-1\">Sources:</p>\n                                  <SourceList items={documentSourceItems} maxVisible={3} />\n                                </div>\n                              </div>\n                            );\n\n                          // PRIORITY 2: Website-Only Results (Medium Priority - Only if no documents)\n                          } else if (hasWebsiteContent) {\n                            answerSource = 'website_only';\n\n                            // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                            const websiteLabel = websiteSourceItems.length > 1 ? 'Extracted Websites' : 'Extracted Website';\n\n                            components.push(\n                              <div key=\"website-priority\" className=\"mb-4 p-4 bg-green-50 rounded-lg border border-green-200 shadow-sm transition-colors duration-300\">\n                                <h4 className=\"font-semibold text-green-800 text-sm mb-3 flex items-center\">\n                                  🌐 Answer from Extracted Websites\n                                </h4>\n                                <InteractiveAnswer\n                                  content={message.website_answer || \"\"}\n                                  query={userQuestion}\n                                  model={message.llm_model}\n                                  chatId={message.chatId}\n                                />\n                                <div className=\"mt-3 pt-3 border-t border-green-200\">\n                                  <p className=\"text-xs text-green-700 font-semibold mb-1\">Sources:</p>\n                                  <SourceList items={websiteSourceItems} maxVisible={3} />\n                                </div>\n                              </div>\n                            );\n\n                          // Case 4: No answer from either - fallback to LLM\n                          } else if (hasLLMFallback) {\n                            answerSource = 'llm';\n\n                            const modelName = message.llm_model || 'Gemini';\n                            const modelLogo = message.llm_model?.includes('chatgpt') ? '🤖' :\n                                            message.llm_model?.includes('groq') ? '⚡' :\n                                            message.llm_model?.includes('deepseek') ? '🔍' :\n                                            message.llm_model?.includes('qwen') ? '🌐' :\n                                            message.llm_model?.includes('ollama') ? '🏠' :\n                                            message.llm_model?.includes('huggingface') ? '🤗' : '🧠';\n\n                            // Only show the LLM fallback card if not in loading state\n                            if (!showTrainLoader || !message.loading) {\n                            components.push(\n                              <div key=\"llm-fallback\" className=\"mb-4 p-4 bg-purple-50 rounded-lg border border-purple-200 shadow-sm transition-colors duration-300\">\n                                <h4 className=\"font-semibold text-purple-800 text-sm mb-3 flex items-center\">\n                                  {modelLogo} Answer generated by {modelName}\n                                </h4>\n                                <InteractiveAnswer\n                                  content={message.content || \"I couldn't find any relevant information to answer your question.\"}\n                                  query={userQuestion}\n                                  model={message.llm_model || 'Gemini'}\n                                  chatId={message.chatId}\n                                />\n                                <div className=\"mt-3 pt-3 border-t border-purple-200\">\n                                  <p className=\"text-xs text-purple-600 italic\">\n                                    This answer was generated by an AI model as no relevant information was found in your documents or websites.\n                                  </p>\n                                </div>\n                              </div>\n                            );\n                            }\n\n                          // Case 5: No sources found and fallback disabled (or similar edge case)\n                          } else {\n                            // eslint-disable-next-line @typescript-eslint/no-unused-vars\n                            answerSource = 'no_results';\n\n                            // Only show the \"no results\" card if not in loading state\n                            if (!showTrainLoader || !message.loading) {\n                            components.push(\n                              <div key=\"no-results\" className=\"mt-3 p-4 bg-gray-50 rounded-lg border border-gray-200 transition-colors duration-300\">\n                                <p className=\"text-sm text-gray-600 mb-2\">No sources found</p>\n                                <InteractiveAnswer\n                                  content={message.content || \"I couldn't process your question. Please try rephrasing or check if documents/websites are uploaded.\"}\n                                  query={userQuestion}\n                                  model={message.llm_model || 'Gemini'}\n                                  chatId={message.chatId}\n                                />\n                              </div>\n                            );\n                            }\n                          }\n\n                          // If we have components to display, render them\n                          if (components.length > 0) {\n                            return (\n                              <div className=\"mt-3\">\n                                {components}\n                              </div>\n                            );\n                          }\n\n                          // Fallback for any unhandled edge cases (should rarely happen)\n                          console.warn(\"Frontend: Unhandled rendering case\");\n                          return (\n                            <div className=\"mt-3 p-4 bg-gray-50 rounded-lg border border-gray-200 transition-colors duration-300\">\n                              <p className=\"text-sm text-gray-600 mb-2\">Rendering Error</p>\n                              <InteractiveAnswer\n                                content={message.content || \"An error occurred while rendering the response.\"}\n                                query={userQuestion}\n                                model={message.llm_model || 'Gemini'}\n                                chatId={message.chatId}\n                              />\n                            </div>\n                          );\n                        })()}\n                      </div>\n                    )}\n                  </div>\n                </div>\n              ))}\n              <div ref={messagesEndRef} style={{ float: 'left', clear: 'both' }} />\n            </div>\n          )}\n        </div>\n\n        {/* Fixed Chat Input Box at Bottom - using fixed positioning */}\n        <div className={`border-t border-gray-300 p-4 bg-white fixed bottom-0 right-0 z-20 shadow-lg transition-all duration-300 ${sidebarOpen ? 'lg:left-80 left-0' : 'left-0'}`}>\n          <form onSubmit={handleSendMessage} className=\"flex items-center space-x-2\">\n            <div className=\"flex-1 relative flex\">\n              <input\n                type=\"text\"\n                value={input}\n                onChange={(e) => {\n                  const newValue = e.target.value;\n                  setInput(newValue);\n                  // Don't handle command shortcuts as you type, only on submit\n                }}\n                placeholder=\"Type your message... (/model, /reset, /clear)\"\n                className=\"w-full p-2 border border-gray-300 bg-white text-gray-900 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 transition-colors duration-300\"\n                disabled={isSubmitting}\n                aria-label=\"Message input\"\n              />\n            </div>\n\n            <div className=\"flex items-center space-x-2\">\n              <LLMSelector\n                currentModel={activeLLMModel}\n                onModelChange={(modelId) => setActiveLLMModel(modelId)}\n                isLoading={isSubmitting}\n              />\n              <button\n                type=\"submit\"\n                disabled={isSubmitting || !input.trim()}\n                className=\"bg-blue-500 text-white px-4 py-2 rounded-lg hover:bg-blue-600 focus:outline-none focus:ring-2 focus:ring-blue-500 disabled:opacity-50 flex items-center justify-center transition-colors duration-300\"\n                title=\"Send message\"\n              >\n                <span>{isSubmitting ? \"Sending...\" : \"Send\"}</span>\n              </button>\n            </div>\n          </form>\n                      <div className=\"text-xs text-gray-400 mt-1 text-center\">\n            Current model: {DEFAULT_LLM_MODELS.find(m => m.id === activeLLMModel)?.name || activeLLMModel}\n          </div>\n        </div>\n        {/* Spacer div to push content up above the fixed input box - only needed when there are messages */}\n        {messages.length > 0 && <div className=\"h-36\"></div>}\n      </div>\n    </div>\n  );\n}\n\ninterface AppProps {\n  sidebarOpen: boolean;\n  setSidebarOpen: React.Dispatch<React.SetStateAction<boolean>>;\n}\n\nfunction App({ sidebarOpen, setSidebarOpen }: AppProps) {\n  return (\n    <ChatInterface sidebarOpen={sidebarOpen} setSidebarOpen={setSidebarOpen} />\n  );\n}\n\nexport default App;\n"], "mappings": ";AAAA,OAAOA,KAAK,IAAIC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,QAAQ,OAAO;AAC1D,OAAO,WAAW;AAClB,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,OAAOC,WAAW,IAAIC,kBAAkB,QAAQ,6BAA6B;AAC7E,OAAOC,iBAAiB,MAAM,mCAAmC;AACjE,OAAOC,WAAW,MAAM,+BAA+B;AACvD,OAAOC,WAAW,MAAM,6BAA6B;AACrD,OAAOC,aAAa,MAAM,+BAA+B;AACzD,SAASC,cAAc,QAAQ,wBAAwB;;AAkBvD;AAAA,SAAAC,MAAA,IAAAC,OAAA;AAOA,SAASC,aAAaA,CAAC;EAAEC,WAAW;EAAEC;AAAmC,CAAC,EAAE;EAAA,IAAAC,qBAAA;EAC1E,MAAM;IACJC,cAAc;IACdC,QAAQ;IACRC,aAAa;IACbC,eAAe;IACfC,UAAU;IACVC,aAAa;IACbC;EACF,CAAC,GAAGb,cAAc,CAAC,CAAC;EAEpB,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGzB,QAAQ,CAAS,EAAE,CAAC;EAC9C,MAAM,CAAC0B,YAAY,EAAEC,eAAe,CAAC,GAAG3B,QAAQ,CAAC,KAAK,CAAC;EACvD,MAAM,CAAC4B,cAAc,EAAEC,iBAAiB,CAAC,GAAG7B,QAAQ,CAAC,kBAAkB,CAAC,CAAC,CAAC;EAC1E,MAAM,CAAC8B,eAAe,EAAEC,kBAAkB,CAAC,GAAG/B,QAAQ,CAAC,KAAK,CAAC;EAC7D,MAAM,CAACgC,kBAAkB,EAAEC,qBAAqB,CAAC,GAAGjC,QAAQ,CAAmG,cAAc,CAAC;EAC9K,MAAMkC,cAAc,GAAGjC,MAAM,CAAiB,IAAI,CAAC;;EAEnD;EACAC,SAAS,CAAC,MAAM;IACd,IAAIgC,cAAc,CAACC,OAAO,EAAE;MAC1BD,cAAc,CAACC,OAAO,CAACC,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IAC/D;EACF,CAAC,EAAE,CAACnB,QAAQ,CAAC,CAAC;;EAEd;EACA,MAAMoB,uBAAuB,GAAGA,CAACC,YAAoB,EAAEC,cAAsB,KAAc;IACzF,MAAMC,SAAS,GAAGF,YAAY,CAACG,WAAW,CAAC,CAAC;;IAE5C;IACA,IAAID,SAAS,CAACE,QAAQ,CAAC,OAAO,CAAC,EAAE;MAC/B,OAAO,IAAI;IACb;;IAEA;IACA,MAAMC,cAAc,GAAGH,SAAS,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,SAAS,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,SAAS,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,SAAS,CAACE,QAAQ,CAAC,OAAO,CAAC;IACnJ,MAAME,iBAAiB,GAAG,CAACJ,SAAS,CAACE,QAAQ,CAAC,OAAO,CAAC,IAAIF,SAAS,CAACE,QAAQ,CAAC,WAAW,CAAC,MAC/DF,SAAS,CAACE,QAAQ,CAAC,SAAS,CAAC,IAAIF,SAAS,CAACE,QAAQ,CAAC,eAAe,CAAC,IAAIF,SAAS,CAACE,QAAQ,CAAC,KAAK,CAAC,IAAIF,SAAS,CAACE,QAAQ,CAAC,SAAS,CAAC,CAAC;IAC7J,MAAMG,iBAAiB,GAAGL,SAAS,CAACE,QAAQ,CAAC,eAAe,CAAC,IAAIF,SAAS,CAACE,QAAQ,CAAC,eAAe,CAAC,IAAIF,SAAS,CAACE,QAAQ,CAAC,WAAW,CAAC;IAEvI,MAAMI,WAAW,GAAG,CAAAP,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAEE,WAAW,CAAC,CAAC,KAAI,EAAE;IACvD,MAAMM,gBAAgB,GAAGD,WAAW,CAACJ,QAAQ,CAAC,SAAS,CAAC,IAAII,WAAW,CAACJ,QAAQ,CAAC,GAAG,CAAC,IAC5DI,WAAW,CAACJ,QAAQ,CAAC,OAAO,CAAC,IAAII,WAAW,CAACE,MAAM,GAAG,GAAI;IACnF,MAAMC,mBAAmB,GAAGH,WAAW,CAACJ,QAAQ,CAAC,OAAO,CAAC,IAAII,WAAW,CAACJ,QAAQ,CAAC,QAAQ,CAAC,IAAII,WAAW,CAACJ,QAAQ,CAAC,SAAS,CAAC;IAC9H,MAAMQ,gBAAgB,GAAGJ,WAAW,CAACE,MAAM,GAAG,EAAE;;IAEhD;IACA,IAAIH,iBAAiB,IAAKF,cAAc,KAAKH,SAAS,CAACE,QAAQ,CAAC,MAAM,CAAC,IAAIF,SAAS,CAACE,QAAQ,CAAC,SAAS,CAAC,CAAE,EAAE;MAC1G,OAAO,IAAI;IACb;;IAEA;IACA,IAAIE,iBAAiB,IAAI,CAACD,cAAc,EAAE;MACxC,OAAO,KAAK;IACd;;IAEA;IACA,MAAMQ,UAAU,GAAG,CAACJ,gBAAgB;IAAI;IACtB,CAACE,mBAAmB;IAAI;;IAErBN,cAAc,IAAI,CAACO,gBAAgB;IAAK;IACxC,CAACA,gBAAgB,IAAIJ,WAAW,CAACE,MAAM,GAAG,EAAG,CAAC;IAAA,CAChD;IAEnB,OAAOG,UAAU;EACnB,CAAC;;EAED;EACA,MAAMC,mBAAmB,GAAGA,CAACC,MAAuB,EAAEf,YAAoB,KAAK;IAC7E,IAAI,OAAOe,MAAM,KAAK,QAAQ,EAAE;MAC9B,OAAO,KAAK;IACd;IAEA,MAAMC,cAAc,GAAGhB,YAAY,CAACG,WAAW,CAAC,CAAC;IACjD,MAAMc,SAAS,GAAGF,MAAgB;IAElCG,OAAO,CAACC,GAAG,CAAC,8BAA8B,EAAE;MAC1CC,QAAQ,EAAEH,SAAS,CAACG,QAAQ;MAC5BC,YAAY,EAAEJ,SAAS,CAACI,YAAY;MACpCC,kBAAkB,EAAE,CAAC,CAACL,SAAS,CAACM,cAAc;MAC9CC,KAAK,EAAExB;IACT,CAAC,CAAC;;IAEF;IACA,IAAIgB,cAAc,CAACZ,QAAQ,CAAC,MAAM,CAAC,EAAE;MACnC,MAAMqB,OAAO,GAAGR,SAAS,CAACI,YAAY,KAAK,OAAO;MAElD,IAAII,OAAO,EAAE;QAAA,IAAAC,mBAAA,EAAAC,qBAAA,EAAAC,sBAAA;QACX;QACA,MAAMR,QAAQ,GAAG,EAAAM,mBAAA,GAAAT,SAAS,CAACG,QAAQ,cAAAM,mBAAA,uBAAlBA,mBAAA,CAAoBvB,WAAW,CAAC,CAAC,KAAI,EAAE;QACxD,MAAM0B,gBAAgB,GAAGC,IAAI,CAACC,SAAS,CAACd,SAAS,CAACM,cAAc,IAAI,CAAC,CAAC,CAAC,CAACpB,WAAW,CAAC,CAAC;;QAErF;QACA,MAAM6B,OAAO,GAAGZ,QAAQ,CAAChB,QAAQ,CAAC,MAAM,CAAC,IAC1ByB,gBAAgB,CAACzB,QAAQ,CAAC,MAAM,CAAC,IAChC,EAAAuB,qBAAA,GAAAV,SAAS,CAACM,cAAc,cAAAI,qBAAA,uBAAxBA,qBAAA,CAA0BM,QAAQ,KAClChB,SAAS,CAACM,cAAc,CAACU,QAAQ,CAAC9B,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAE;;QAEjF;QACA,MAAM8B,kBAAkB,GAAGd,QAAQ,CAAChB,QAAQ,CAAC,SAAS,CAAC,IAC7BgB,QAAQ,CAAChB,QAAQ,CAAC,MAAM,CAAC,IACzBgB,QAAQ,CAAChB,QAAQ,CAAC,OAAO,CAAC,IAC1ByB,gBAAgB,CAACzB,QAAQ,CAAC,MAAM,CAAC,IACjCyB,gBAAgB,CAACzB,QAAQ,CAAC,SAAS,CAAC,MAAAwB,sBAAA,GACpCX,SAAS,CAACM,cAAc,cAAAK,sBAAA,uBAAxBA,sBAAA,CAA0BO,OAAO;;QAE3D;QACA,MAAMtB,UAAU,GAAGmB,OAAO,IAAIE,kBAAkB,IAAIT,OAAO;QAE3DP,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;UACvCC,QAAQ;UACRY,OAAO;UACPE,kBAAkB;UAClBrB,UAAU;UACVuB,mBAAmB,EAAEnB,SAAS,CAACM,cAAc,GAAGc,MAAM,CAACC,IAAI,CAACrB,SAAS,CAACM,cAAc,CAAC,GAAG;QAC1F,CAAC,CAAC;QAEF,OAAOV,UAAU;MACnB;MAEA,OAAO,KAAK,CAAC,CAAC;IAChB;;IAEA;IACA,MAAM0B,YAAY,GAAGvB,cAAc,CAACwB,KAAK,CAAC,iBAAiB,CAAC;IAC5D,MAAMC,cAAc,GAAGzB,cAAc,CAACwB,KAAK,CAAC,mBAAmB,CAAC;;IAEhE;IACA,MAAMX,gBAAgB,GAAGC,IAAI,CAACC,SAAS,CAACd,SAAS,CAACM,cAAc,IAAI,CAAC,CAAC,CAAC,CAACpB,WAAW,CAAC,CAAC;;IAErF;IACA,IAAIoC,YAAY,IAAIvB,cAAc,CAACZ,QAAQ,CAAC,OAAO,CAAC,EAAE;MAAA,IAAAsC,sBAAA;MACpD,MAAMC,aAAa,GAAGJ,YAAY,CAAC,CAAC,CAAC;MACrC,MAAMd,OAAO,GAAGR,SAAS,CAACI,YAAY,KAAK,OAAO;;MAElD;MACA,MAAMuB,mBAAmB,GAAGf,gBAAgB,CAACzB,QAAQ,CAAC,WAAWuC,aAAa,EAAE,CAAC,IACrDd,gBAAgB,CAACzB,QAAQ,CAAC,UAAUuC,aAAa,EAAE,CAAC;MAChF,MAAME,oBAAoB,GAAG5B,SAAS,CAACG,QAAQ,IAAIH,SAAS,CAACG,QAAQ,CAACjB,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,WAAWuC,aAAa,EAAE,CAAC;MACxH,MAAMG,eAAe,GAAG7B,SAAS,CAAC8B,IAAI,IAAIC,IAAI,CAACC,GAAG,CAAChC,SAAS,CAAC8B,IAAI,GAAGG,QAAQ,CAACP,aAAa,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC;MACnG,MAAMQ,iBAAiB,GAAG,EAAAT,sBAAA,GAAAzB,SAAS,CAACM,cAAc,cAAAmB,sBAAA,uBAAxBA,sBAAA,CAA0BU,eAAe,KAC1CnC,SAAS,CAACM,cAAc,CAAC6B,eAAe,CAAChD,QAAQ,CAAC,WAAWuC,aAAa,EAAE,CAAC;MAEtG,MAAMU,iBAAiB,GAAGT,mBAAmB,IAAIC,oBAAoB,IAAIC,eAAe,IAAIK,iBAAiB;;MAE7G;MACA;MACA,OAAO1B,OAAO,IAAI4B,iBAAiB;IACrC;;IAEA;IACA,IAAIZ,cAAc,IAAIzB,cAAc,CAACZ,QAAQ,CAAC,OAAO,CAAC,EAAE;MACtD,MAAMkD,eAAe,GAAGb,cAAc,CAAC,CAAC,CAAC;MACzC,MAAMc,OAAO,GAAGtC,SAAS,CAACI,YAAY,KAAK,OAAO;;MAElD;MACA,MAAMmC,mBAAmB,GAAG3B,gBAAgB,CAACzB,QAAQ,CAAC,aAAakD,eAAe,EAAE,CAAC,IACzDzB,gBAAgB,CAACzB,QAAQ,CAAC,YAAYkD,eAAe,EAAE,CAAC,IACxDzB,gBAAgB,CAACzB,QAAQ,CAAC,SAASkD,eAAe,EAAE,CAAC;MAEjF,OAAOC,OAAO,IAAIC,mBAAmB;IACvC;;IAEA;IACA,IAAIxC,cAAc,CAACZ,QAAQ,CAAC,OAAO,CAAC,IAAI,CAACY,cAAc,CAACZ,QAAQ,CAAC,OAAO,CAAC,EAAE;MACzE,MAAMqB,OAAO,GAAGR,SAAS,CAACI,YAAY,KAAK,OAAO;MAClD,OAAOI,OAAO;IAChB;;IAEA;IACA,IAAIT,cAAc,CAACZ,QAAQ,CAAC,OAAO,CAAC,IAAI,CAACY,cAAc,CAACZ,QAAQ,CAAC,OAAO,CAAC,EAAE;MACzE,MAAMmD,OAAO,GAAGtC,SAAS,CAACI,YAAY,KAAK,OAAO;MAClD,OAAOkC,OAAO;IAChB;;IAEA;IACA,OAAO,IAAI;EACb,CAAC;;EAED;EACA,MAAME,qBAAqB,GAAIxE,KAAa,IAAK;IAC/C;IACA,IAAIA,KAAK,CAACyE,UAAU,CAAC,GAAG,CAAC,EAAE;MACzB,MAAMC,OAAO,GAAG1E,KAAK,CAAC2E,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACzD,WAAW,CAAC,CAAC;;MAEjD;MACA,IAAIwD,OAAO,KAAK,QAAQ,EAAE;QACxB,MAAME,QAAQ,GAAG5E,KAAK,CAAC6E,SAAS,CAAC,CAAC,CAAC,CAACC,IAAI,CAAC,CAAC;QAC1C,MAAMC,YAAY,GAAGlG,kBAAkB,CAACmG,IAAI,CAACC,CAAC,IAC5CA,CAAC,CAACC,IAAI,CAAChE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACyD,QAAQ,CAAC1D,WAAW,CAAC,CAAC,CAAC,IACrD+D,CAAC,CAACE,EAAE,CAACjE,WAAW,CAAC,CAAC,CAACC,QAAQ,CAACyD,QAAQ,CAAC1D,WAAW,CAAC,CAAC,CACpD,CAAC;QAED,IAAI6D,YAAY,IAAIA,YAAY,CAACK,OAAO,EAAE;UACxC/E,iBAAiB,CAAC0E,YAAY,CAACI,EAAE,CAAC;UAClClF,QAAQ,CAAC,EAAE,CAAC;UACZ,OAAO,WAAW;QACpB;MACF;;MAEA;MAAA,KACK,IAAIyE,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,QAAQ,EAAE;QACrD3E,gBAAgB,CAAC,CAAC;QAClBE,QAAQ,CAAC,EAAE,CAAC;QACZ,OAAO,WAAW;MACpB;IACF;IAEA,OAAO,eAAe;EACxB,CAAC;EAED,MAAMoF,iBAAiB,GAAG,MAAOC,CAAkB,IAAK;IACtDA,CAAC,CAACC,cAAc,CAAC,CAAC;IAClB,IAAI,CAACvF,KAAK,CAAC8E,IAAI,CAAC,CAAC,IAAI5E,YAAY,EAAE;;IAEnC;IACA,IAAIF,KAAK,CAACyE,UAAU,CAAC,GAAG,CAAC,EAAE;MACzB,MAAMe,MAAM,GAAGhB,qBAAqB,CAACxE,KAAK,CAAC;MAC3C,IAAIwF,MAAM,KAAK,WAAW,EAAE;QAC1BvF,QAAQ,CAAC,EAAE,CAAC;QACZ;MACF;MACA;IACF;IAEA,OAAO,MAAMwF,eAAe,CAACzF,KAAK,CAAC;EACrC,CAAC;EAED,MAAMyF,eAAe,GAAG,MAAOC,WAAmB,IAAK;IAAA,IAAAC,qBAAA;IACrD;IACA,IAAI,CAAClG,cAAc,EAAE;MACnB,MAAME,aAAa,CAAC,CAAC;IACvB;IAEA,MAAMiG,WAAwB,GAAG;MAC/BT,EAAE,EAAE,QAAQU,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE;MACxBC,OAAO,EAAEL,WAAW;MACpBM,MAAM,EAAE,MAAM;MACdC,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC;MACnCC,MAAM,EAAE,CAAA1G,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE0F,EAAE,KAAI;IAChC,CAAC;IAEDtF,UAAU,CAAC+F,WAAW,CAAC;IACvB3F,QAAQ,CAAC,EAAE,CAAC;IAEZ,MAAMmG,SAAS,GAAGP,IAAI,CAACC,GAAG,CAAC,CAAC;IAC5B,MAAMO,aAA0B,GAAG;MACjClB,EAAE,EAAE,MAAMiB,SAAS,EAAE;MACrBL,OAAO,EAAE,EAAE;MACXC,MAAM,EAAE,IAAI;MACZM,OAAO,EAAE,IAAI;MACbL,SAAS,EAAE,IAAIJ,IAAI,CAAC,CAAC,CAACK,WAAW,CAAC,CAAC;MACnCK,SAAS,EAAEnG,cAAc;MACzB+F,MAAM,EAAE,CAAA1G,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE0F,EAAE,KAAI;IAChC,CAAC;IAEDtF,UAAU,CAACwG,aAAa,CAAC;IACzB,CAAAV,qBAAA,GAAAjF,cAAc,CAACC,OAAO,cAAAgF,qBAAA,uBAAtBA,qBAAA,CAAwB/E,cAAc,CAAC;MAAEC,QAAQ,EAAE;IAAS,CAAC,CAAC;IAE9DV,eAAe,CAAC,IAAI,CAAC;IACrBI,kBAAkB,CAAC,IAAI,CAAC;IACxBE,qBAAqB,CAAC,cAAc,CAAC;IAErC,IAAI;MAAA,IAAA+F,sBAAA,EAAAC,sBAAA;MACF,CAAAD,sBAAA,GAAA9F,cAAc,CAACC,OAAO,cAAA6F,sBAAA,uBAAtBA,sBAAA,CAAwB5F,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;;MAE9D;MACA6F,UAAU,CAAC,MAAMjG,qBAAqB,CAAC,qBAAqB,CAAC,EAAE,GAAG,CAAC;MACnEiG,UAAU,CAAC,MAAMjG,qBAAqB,CAAC,oBAAoB,CAAC,EAAE,IAAI,CAAC;MACnEiG,UAAU,CAAC,MAAMjG,qBAAqB,CAAC,mBAAmB,CAAC,EAAE,IAAI,CAAC;MAElE,MAAMkG,QAAQ,GAAG,MAAMhI,SAAS,CAAC+G,WAAW,EAAEtF,cAAc,CAAC;;MAE7D;MACA,MAAMwG,SAAsB,GAAG;QAC7BzB,EAAE,EAAE,MAAMiB,SAAS,EAAE;QACrBL,OAAO,EAAEY,QAAQ,CAACE,MAAM;QACxBC,eAAe,EAAEH,QAAQ,CAACG,eAAe;QACzCC,cAAc,EAAEJ,QAAQ,CAACI,cAAc;QACvCR,SAAS,EAAEI,QAAQ,CAACJ,SAAS,IAAInG,cAAc;QAC/C4F,MAAM,EAAE,IAAI;QACZgB,OAAO,EAAEL,QAAQ,CAACK,OAAO;QACzBC,gBAAgB,EAAEN,QAAQ,CAACM,gBAAgB;QAC3CC,eAAe,EAAEP,QAAQ,CAACO,eAAe;QACzCf,MAAM,EAAE,CAAA1G,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE0F,EAAE,KAAI,MAAM;QACpCgC,iBAAiB,EAAER,QAAQ,CAACQ,iBAAiB,CAAG;MAClD,CAAC;MAEDrH,aAAa,CAACuG,aAAa,CAAClB,EAAE,EAAEyB,SAAS,CAAC;MAE1C,CAAAH,sBAAA,GAAA/F,cAAc,CAACC,OAAO,cAAA8F,sBAAA,uBAAtBA,sBAAA,CAAwB7F,cAAc,CAAC;QAAEC,QAAQ,EAAE;MAAS,CAAC,CAAC;IAChE,CAAC,CAAC,OAAOuG,KAAK,EAAE;MACdnF,OAAO,CAACmF,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;;MAE9C;MACA,MAAMC,YAAyB,GAAG;QAChClC,EAAE,EAAE,MAAMiB,SAAS,EAAE;QACrBL,OAAO,EAAE,iNAAiN;QAC1NC,MAAM,EAAE,IAAI;QACZG,MAAM,EAAE,CAAA1G,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE0F,EAAE,KAAI,MAAM;QACpCgC,iBAAiB,EAAE,IAAI,CAAG;MAC5B,CAAC;MAEDrH,aAAa,CAACuG,aAAa,CAAClB,EAAE,EAAEkC,YAAY,CAAC;IAC/C,CAAC,SAAS;MACRlH,eAAe,CAAC,KAAK,CAAC;MACtBI,kBAAkB,CAAC,KAAK,CAAC;MACzBE,qBAAqB,CAAC,UAAU,CAAC;IACnC;EACF,CAAC;EAED,MAAM6G,sBAAsB,GAAIN,OAAgC,IAAK;IACnE,IAAI,CAACA,OAAO,IAAIA,OAAO,CAACvF,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;;IAE/C;IACA,MAAM8F,cAAwE,GAAG,CAAC,CAAC;IAEnFP,OAAO,CAACQ,OAAO,CAAC1F,MAAM,IAAI;MACxB,IAAIK,QAAgB;MACpB,IAAI2B,IAAuB;MAE3B,IAAI,OAAOhC,MAAM,KAAK,QAAQ,EAAE;QAC9B;QACA,MAAMyB,KAAK,GAAGzB,MAAM,CAACyB,KAAK,CAAC,kCAAkC,CAAC;QAC9DpB,QAAQ,GAAGoB,KAAK,GAAGA,KAAK,CAAC,CAAC,CAAC,CAACuB,IAAI,CAAC,CAAC,GAAGhD,MAAM;QAC3CgC,IAAI,GAAGP,KAAK,IAAIA,KAAK,CAAC,CAAC,CAAC,GAAGU,QAAQ,CAACV,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;MACnD,CAAC,MAAM;QACLpB,QAAQ,GAAGL,MAAM,CAACoD,IAAI,IAAIpD,MAAM,CAACK,QAAQ,IAAI,kBAAkB;QAC/D;QACA,IAAKL,MAAM,CAAS2F,KAAK,IAAIC,KAAK,CAACC,OAAO,CAAE7F,MAAM,CAAS2F,KAAK,CAAC,IAAK3F,MAAM,CAAS2F,KAAK,CAAChG,MAAM,GAAG,CAAC,EAAE;UACrGqC,IAAI,GAAIhC,MAAM,CAAS2F,KAAiB;QAC1C,CAAC,MAAM;UACL3D,IAAI,GAAGhC,MAAM,CAACgC,IAAI,IAAI,CAAC;QACzB;MACF;MAEA,IAAI,CAACyD,cAAc,CAACpF,QAAQ,CAAC,EAAE;QAC7BoF,cAAc,CAACpF,QAAQ,CAAC,GAAG;UAAEA,QAAQ;UAAEsF,KAAK,EAAE;QAAG,CAAC;MACpD;MAEA,MAAMG,OAAO,GAAIC,CAAS,IAAK;QAC7B,IAAI,CAACN,cAAc,CAACpF,QAAQ,CAAC,CAACsF,KAAK,CAACtG,QAAQ,CAAC0G,CAAC,CAAC,EAAE;UAC/CN,cAAc,CAACpF,QAAQ,CAAC,CAACsF,KAAK,CAACK,IAAI,CAACD,CAAC,CAAC;QACxC;MACF,CAAC;MAED,IAAIH,KAAK,CAACC,OAAO,CAAC7D,IAAI,CAAC,EAAE;QACvBA,IAAI,CAAC0D,OAAO,CAACI,OAAO,CAAC;MACvB,CAAC,MAAM;QACLA,OAAO,CAAC9D,IAAc,CAAC;MACzB;IACF,CAAC,CAAC;;IAEF;IACA,OAAOV,MAAM,CAAC2E,MAAM,CAACR,cAAc,CAAC,CAACS,GAAG,CAACC,KAAK,IAAI;MAChD,MAAMC,WAAW,GAAGD,KAAK,CAACR,KAAK,CAACU,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAKD,CAAC,GAAGC,CAAC,CAAC;MACrD,MAAMC,QAAQ,GAAGJ,WAAW,CAACzG,MAAM,KAAK,CAAC,GACrC,QAAQyG,WAAW,CAAC,CAAC,CAAC,EAAE,GACxB,SAASA,WAAW,CAACK,IAAI,CAAC,IAAI,CAAC,EAAE;;MAErC;MACA,OAAO;QACLC,IAAI,EAAE,GAAGP,KAAK,CAAC9F,QAAQ,MAAMmG,QAAQ,EAAE;QACvCG,IAAI,EAAE,gBAAgBC,kBAAkB,CAACT,KAAK,CAAC9F,QAAQ,CAAC,SAAS+F,WAAW,CAAC,CAAC,CAAC,EAAE;QACjFS,UAAU,EAAE;MACd,CAAC;IACH,CAAC,CAAC;EACJ,CAAC;EAED,MAAMC,qBAAqB,GAAI5B,OAAgC,IAAK;IAClE,IAAI,CAACA,OAAO,IAAIA,OAAO,CAACvF,MAAM,KAAK,CAAC,EAAE,OAAO,EAAE;;IAE/C;IACA,MAAMoH,UAAU,GAAG,IAAIC,GAAG,CAAS,CAAC;IACpC,MAAMC,SAAsE,GAAG,EAAE;IAEjF/B,OAAO,CAACQ,OAAO,CAAC1F,MAAM,IAAI;MACxB,IAAIkH,GAAW;MACf,IAAIC,WAAmB;MAEvB,IAAI,OAAOnH,MAAM,KAAK,QAAQ,EAAE;QAC9BkH,GAAG,GAAGlH,MAAM,CAAC2C,UAAU,CAAC,MAAM,CAAC,GAAG3C,MAAM,GAAG,WAAWA,MAAM,EAAE;QAC5D,IAAI;UACJ,MAAMoH,MAAM,GAAG,IAAIC,GAAG,CAACH,GAAG,CAAC;UAC3BC,WAAW,GAAGC,MAAM,CAACE,QAAQ,CAACC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;QACnD,CAAC,CAAC,MAAM;UACRJ,WAAW,GAAGnH,MAAM;QACtB;MACF,CAAC,MAAM;QACLkH,GAAG,GAAGlH,MAAM,CAACkH,GAAG,IAAI,uCAAuC;QAC3D,IAAI;UACF,MAAME,MAAM,GAAG,IAAIC,GAAG,CAACH,GAAG,CAAC;UAC3BC,WAAW,GAAGC,MAAM,CAACE,QAAQ,CAACC,OAAO,CAAC,QAAQ,EAAE,EAAE,CAAC;QACrD,CAAC,CAAC,MAAM;UACNJ,WAAW,GAAGD,GAAG;QACnB;MACF;MAEA,IAAI,CAACH,UAAU,CAACS,GAAG,CAACN,GAAG,CAAC,EAAE;QACxBH,UAAU,CAACU,GAAG,CAACP,GAAG,CAAC;QACnBD,SAAS,CAACjB,IAAI,CAAC;UACbU,IAAI,EAAES,WAAW;UACjBR,IAAI,EAAEO,GAAG;UACTL,UAAU,EAAE,KAAK,CAAE;QACrB,CAAC,CAAC;MACF;IACJ,CAAC,CAAC;IAEF,OAAOI,SAAS,CAAC,CAAC;EACpB,CAAC;;EAED;EACA,MAAMS,UAAU,GAAGA,CAAC;IAAEC,KAAK;IAAEC,UAAU,GAAG;EAG1C,CAAC,KAAK;IACJ,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC,GAAGpL,QAAQ,CAACiL,KAAK,CAAChI,MAAM,IAAIiI,UAAU,CAAC;IAEpE,IAAID,KAAK,CAAChI,MAAM,KAAK,CAAC,EAAE,OAAO,IAAI;IAEnC,MAAMoI,YAAY,GAAGF,QAAQ,GAAGF,KAAK,GAAGA,KAAK,CAACK,KAAK,CAAC,CAAC,EAAEJ,UAAU,CAAC;IAClE,MAAMK,OAAO,GAAGN,KAAK,CAAChI,MAAM,GAAGiI,UAAU;IAEzC,MAAMM,mBAAmB,GAAGA,CAAC1E,CAAsC,EAAEmD,IAAY,KAAK;MACpF;MACA;MACA;IAAA,CACD;IAED,oBACErJ,OAAA;MAAI6K,SAAS,EAAC,uCAAuC;MAAAC,QAAA,GAClDL,YAAY,CAAC7B,GAAG,CAAC,CAACmC,IAAI,EAAEC,KAAK,kBACxBhL,OAAA;QAAA8K,QAAA,EACGC,IAAI,CAAC1B,IAAI,gBACZrJ,OAAA;UACEiL,IAAI,EAAEF,IAAI,CAAC1B,IAAK;UAChB6B,MAAM,EAAC,QAAQ;UACfC,GAAG,EAAC,qBAAqB;UACzBN,SAAS,EAAC,8DAA8D;UACxEO,KAAK,EAAEL,IAAI,CAACxB,UAAU,GAAG,4BAA4B,GAAG,yBAA0B;UAClF8B,OAAO,EAAEN,IAAI,CAACxB,UAAU,GAAIrD,CAAC,IAAK0E,mBAAmB,CAAC1E,CAAC,EAAE6E,IAAI,CAAC1B,IAAK,CAAC,GAAGiC,SAAU;UAAAR,QAAA,EAE5EC,IAAI,CAAC3B;QAAI;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACT,CAAC,gBAER1L,OAAA;UAAM6K,SAAS,EAAC,eAAe;UAAAC,QAAA,EAAEC,IAAI,CAAC3B;QAAI;UAAAmC,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAO;MAC9C,GAdMV,KAAK;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAeV,CACL,CAAC,EACLf,OAAO,IAAI,CAACJ,QAAQ,iBACnBvK,OAAA;QAAI6K,SAAS,EAAC,WAAW;QAAAC,QAAA,eACvB9K,OAAA;UACEqL,OAAO,EAAEA,CAAA,KAAMb,WAAW,CAAC,IAAI,CAAE;UACjCK,SAAS,EAAC,sEAAsE;UAAAC,QAAA,GACjF,IACG,EAACT,KAAK,CAAChI,MAAM,GAAGiI,UAAU,EAAC,eAC/B;QAAA;UAAAiB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CACL,EACAf,OAAO,IAAIJ,QAAQ,iBAClBvK,OAAA;QAAI6K,SAAS,EAAC,WAAW;QAAAC,QAAA,eACrB9K,OAAA;UACEqL,OAAO,EAAEA,CAAA,KAAMb,WAAW,CAAC,KAAK,CAAE;UACpCK,SAAS,EAAC,sEAAsE;UAAAC,QAAA,EAC/E;QAED;UAAAS,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAAQ;MAAC;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACP,CACC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACP,CAAC;EAET,CAAC;;EAED;EACA,MAAMC,gBAAgB,GAAG,MAAOC,WAAwB,IAAK;IAC3D;IACA,MAAMpL,eAAe,CAACoL,WAAW,CAAC7F,EAAE,CAAC;IACrC9E,iBAAiB,CAAC2K,WAAW,CAACC,UAAU,IAAI,kBAAkB,CAAC;IAC/D1L,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;EACzB,CAAC;EAED,MAAM2L,aAAa,GAAG,MAAAA,CAAA,KAAY;IAChCjJ,OAAO,CAACC,GAAG,CAAC,sBAAsB,CAAC;IACnC,MAAMvC,aAAa,CAAC,CAAC;IACrBJ,cAAc,CAAC,KAAK,CAAC,CAAC,CAAC;EACzB,CAAC;EAED,oBACEH,OAAA;IAAK6K,SAAS,EAAC,0DAA0D;IAAAC,QAAA,gBAEvE9K,OAAA,CAACJ,WAAW;MACVmM,SAAS,EAAE7K,eAAgB;MAC3B8K,OAAO,EAAE,CAAC,MAAM;QACd,QAAQ5K,kBAAkB;UACxB,KAAK,qBAAqB;YACxB,OAAO,mCAAmC;UAC5C,KAAK,oBAAoB;YACvB,OAAO,kCAAkC;UAC3C,KAAK,mBAAmB;YACtB,OAAO,gCAAgC;UACzC;YACE,OAAO,kCAAkC;QAC7C;MACF,CAAC,EAAE,CAAE;MACL6K,SAAS,EAAC,SAAS;MACnBC,YAAY,EAAE9K,kBAAmB;MACjClB,WAAW,EAAEA;IAAY;MAAAqL,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC,eAGF1L,OAAA,CAACL,WAAW;MACVwM,MAAM,EAAEjM,WAAY;MACpBkM,QAAQ,EAAEA,CAAA,KAAMjM,cAAc,CAAC,CAACD,WAAW,CAAE;MAC7CmM,aAAa,EAAE,CAAAhM,cAAc,aAAdA,cAAc,uBAAdA,cAAc,CAAE0F,EAAE,KAAI,EAAG;MACxCuG,YAAY,EAAEX,gBAAiB;MAC/BY,SAAS,EAAET;IAAc;MAAAP,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OAC1B,CAAC,eAGF1L,OAAA;MAAK6K,SAAS,EAAE,oDAAoD3K,WAAW,GAAG,UAAU,GAAG,EAAE,EAAG;MAAA4K,QAAA,gBAElG9K,OAAA;QAAK6K,SAAS,EAAE,UAAUvK,QAAQ,CAAC+B,MAAM,GAAG,CAAC,GAAG,iBAAiB,GAAG,iBAAiB,YAAa;QAAAyI,QAAA,EAC/FxK,QAAQ,CAAC+B,MAAM,KAAK,CAAC,gBACpBrC,OAAA;UAAK6K,SAAS,EAAC,yCAAyC;UAAAC,QAAA,eACtD9K,OAAA;YAAK6K,SAAS,EAAC,2BAA2B;YAAAC,QAAA,gBACxC9K,OAAA;cAAG6K,SAAS,EAAC,4BAA4B;cAAAC,QAAA,EAAC;YAAmB;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC,eACjE1L,OAAA;cAAA8K,QAAA,EAAG;YAAsC;cAAAS,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAAG,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAC1C;QAAC;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACH,CAAC,gBAEN1L,OAAA;UAAA8K,QAAA,GACGxK,QAAQ,CAACsI,GAAG,CAAEoD,OAAO,iBACpBhM,OAAA;YAEE6K,SAAS,EAAE,QACTmB,OAAO,CAACpF,MAAM,KAAK,MAAM,GAAG,kBAAkB,GAAG,oBAAoB,EACpE;YAAAkE,QAAA,eAEH9K,OAAA;cACE6K,SAAS,EAAE,2DACTmB,OAAO,CAACpF,MAAM,KAAK,MAAM,GACrB,wBAAwB,GACxB,kCAAkC,EACrC;cAAAkE,QAAA,gBAEH9K,OAAA;gBAAK6K,SAAS,EAAC,uCAAuC;gBAAAC,QAAA,gBACpD9K,OAAA;kBAAM6K,SAAS,EAAC,eAAe;kBAAAC,QAAA,EAC5BkB,OAAO,CAACpF,MAAM,KAAK,MAAM,GAAG,KAAK,GAAG;gBAAS;kBAAA2E,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC1C,CAAC,EACNM,OAAO,CAACnF,SAAS,iBAChB7G,OAAA;kBAAM6K,SAAS,EAAE,gBACfmB,OAAO,CAACpF,MAAM,KAAK,MAAM,GACrB,eAAe,GACf,eAAe,EAClB;kBAAAkE,QAAA,EACA,IAAIrE,IAAI,CAACuF,OAAO,CAACnF,SAAS,CAAC,CAAC2F,kBAAkB,CAAC;gBAAC;kBAAAjB,QAAA,EAAAC,YAAA;kBAAAC,UAAA;kBAAAC,YAAA;gBAAA,OAC7C,CACP;cAAA;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACE,CAAC,EAGLM,OAAO,CAACpF,MAAM,KAAK,MAAM,IAAIoF,OAAO,CAACrF,OAAO,iBAC3C3G,OAAA;gBAAK6K,SAAS,EAAC,0BAA0B;gBAAAC,QAAA,EAAEkB,OAAO,CAACrF;cAAO;gBAAA4E,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAM,CACjE,EAGAM,OAAO,CAACpF,MAAM,KAAK,IAAI,iBACtB5G,OAAA;gBAAA8K,QAAA,EACG,CAAC,CAAA2B,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,EAAAC,qBAAA,KAAM;kBACN;kBACA,IAAIZ,OAAO,CAAC9E,OAAO,IAAIhG,eAAe,IAAI,CAAC8K,OAAO,CAACrF,OAAO,IAAI,CAACqF,OAAO,CAACtE,eAAe,IAAI,CAACsE,OAAO,CAACrE,cAAc,EAAE;oBACjH,OAAO,IAAI;kBACb;;kBAEA;kBACA,MAAMkF,mBAAmB,GAAG3E,sBAAsB,CAAC8D,OAAO,CAACnE,gBAAgB,CAAC;kBAC5E,MAAMiF,kBAAkB,GAAGtD,qBAAqB,CAACwC,OAAO,CAAClE,eAAe,CAAC;;kBAEzE;kBACA,MAAMiF,kBAAkB,GAAG,CAAC,EAAEf,OAAO,CAACtE,eAAe,IAAIsE,OAAO,CAACtE,eAAe,CAAChC,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;kBAC/F,MAAMsH,iBAAiB,GAAG,CAAC,EAAEhB,OAAO,CAACrE,cAAc,IAAIqE,OAAO,CAACrE,cAAc,CAACjC,IAAI,CAAC,CAAC,KAAK,EAAE,CAAC;kBAC5F;kBACA,MAAMuH,cAAc,GAAGjB,OAAO,CAACjE,iBAAiB,IAAK,CAACgF,kBAAkB,IAAI,CAACC,iBAAkB;;kBAE/F;kBACAnK,OAAO,CAACC,GAAG,CAAC,wBAAwBkJ,OAAO,CAACjG,EAAE,GAAG,EAAE;oBACjDgH,kBAAkB;oBAClBC,iBAAiB;oBACjBC,cAAc;oBACdC,oBAAoB,EAAE,EAAAT,qBAAA,GAAAT,OAAO,CAACtE,eAAe,cAAA+E,qBAAA,uBAAvBA,qBAAA,CAAyBpK,MAAM,KAAI,CAAC;oBAC1D8K,mBAAmB,EAAE,EAAAT,qBAAA,GAAAV,OAAO,CAACrE,cAAc,cAAA+E,qBAAA,uBAAtBA,qBAAA,CAAwBrK,MAAM,KAAI,CAAC;oBACxD+K,oBAAoB,EAAEP,mBAAmB,CAACxK,MAAM;oBAChDgL,mBAAmB,EAAEP,kBAAkB,CAACzK,MAAM;oBAC9CiL,iBAAiB,EAAEtB,OAAO,CAACtE,eAAe,GAAG,QAAQ,GAAG,SAAS;oBACjE6F,gBAAgB,EAAEvB,OAAO,CAACrE,cAAc,GAAG,QAAQ,GAAG,SAAS;oBAC/D6F,kBAAkB,EAAE,EAAAb,qBAAA,GAAAX,OAAO,CAACnE,gBAAgB,cAAA8E,qBAAA,uBAAxBA,qBAAA,CAA0BtK,MAAM,KAAI,CAAC;oBACzDoL,iBAAiB,EAAE,EAAAb,qBAAA,GAAAZ,OAAO,CAAClE,eAAe,cAAA8E,qBAAA,uBAAvBA,qBAAA,CAAyBvK,MAAM,KAAI;kBACxD,CAAC,CAAC;;kBAEF;kBACA,MAAMqL,mBAAmB,GAAGpN,QAAQ,CAACqN,SAAS,CAAC9H,CAAC,IAAIA,CAAC,CAACE,EAAE,KAAKiG,OAAO,CAACjG,EAAE,CAAC;kBACxE,IAAIpE,YAAY,GAAG,EAAE;;kBAErB;kBACA,KAAK,IAAIiM,CAAC,GAAGF,mBAAmB,GAAG,CAAC,EAAEE,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;oBACjD,IAAItN,QAAQ,CAACsN,CAAC,CAAC,CAAChH,MAAM,KAAK,MAAM,IAAItG,QAAQ,CAACsN,CAAC,CAAC,CAACjH,OAAO,EAAE;sBACxDhF,YAAY,GAAGrB,QAAQ,CAACsN,CAAC,CAAC,CAACjH,OAAO;sBAClC;oBACF;kBACF;kBAEA9D,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAE;oBAAE+K,WAAW,EAAE7B,OAAO,CAACjG,EAAE;oBAAEpE;kBAAa,CAAC,CAAC;;kBAEvG;kBACA,MAAMmM,UAAU,GAAG,EAAE;kBACrB;kBACA,IAAIC,YAAY,GAAG,EAAE;;kBAErB;;kBAEA;kBACA,IAAIhB,kBAAkB,EAAE;oBACtBgB,YAAY,GAAG,eAAe;oBAE9BD,UAAU,CAACpF,IAAI,cACb1I,OAAA;sBAA6B6K,SAAS,EAAC,gGAAgG;sBAAAC,QAAA,gBACrI9K,OAAA;wBAAI6K,SAAS,EAAC,4DAA4D;wBAAAC,QAAA,GAAC,+BACtD,EAAC+B,mBAAmB,CAACxK,MAAM,GAAG,CAAC,GAAG,WAAW,GAAG,UAAU;sBAAA;wBAAAkJ,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAC3E,CAAC,eACL1L,OAAA,CAACN,iBAAiB;wBAChBiH,OAAO,EAAEqF,OAAO,CAACtE,eAAe,IAAI,EAAG;wBACvCvE,KAAK,EAAExB,YAAa;wBACpBqM,KAAK,EAAEhC,OAAO,CAAC7E,SAAU;wBACzBJ,MAAM,EAAEiF,OAAO,CAACjF;sBAAO;wBAAAwE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxB,CAAC,EAGD,CAACuC,sBAAA,IAAM;wBACN,MAAMC,aAAa,GAAG,EAAAD,sBAAA,GAAAjC,OAAO,CAACnE,gBAAgB,cAAAoG,sBAAA,uBAAxBA,sBAAA,CAA0BE,MAAM,CAACzL,MAAM,IAC3D,OAAOA,MAAM,KAAK,QAAQ,IAAIA,MAAM,CAACQ,cACvC,CAAC,KAAI,EAAE;wBAEPL,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEoL,aAAa,CAAC7L,MAAM,CAAC;wBAC7DQ,OAAO,CAACC,GAAG,CAAC,oBAAoB,EAAEoL,aAAa,CAACtF,GAAG,CAACwF,CAAC,KAAK;0BACxDrL,QAAQ,EAAGqL,CAAC,CAAYrL,QAAQ;0BAChCC,YAAY,EAAGoL,CAAC,CAAYpL,YAAY;0BACxCC,kBAAkB,EAAE,CAAC,CAAEmL,CAAC,CAAYlL;wBACtC,CAAC,CAAC,CAAC,CAAC;;wBAEJ;wBACA,IAAImL,eAAe,GAAGH,aAAa,CAACC,MAAM,CAACzL,MAAM,IAC/CD,mBAAmB,CAACC,MAAM,EAAYf,YAAY,CACpD,CAAC;wBAEDkB,OAAO,CAACC,GAAG,CAAC,sCAAsC,EAAEuL,eAAe,CAAChM,MAAM,CAAC;;wBAE3E;wBACA,IAAIgM,eAAe,CAAChM,MAAM,KAAK,CAAC,KAAKV,YAAY,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,OAAO,CAAC,IAAIJ,YAAY,CAACG,WAAW,CAAC,CAAC,CAACC,QAAQ,CAAC,MAAM,CAAC,CAAC,EAAE;0BACjIsM,eAAe,GAAGH,aAAa,CAACC,MAAM,CAACzL,MAAM,IAC1CA,MAAM,CAAYM,YAAY,KAAK,OACtC,CAAC;0BACDH,OAAO,CAACC,GAAG,CAAC,mFAAmF,EAAEuL,eAAe,CAAChM,MAAM,CAAC;wBAC1H;wBAEA,OAAOgM,eAAe,CAAChM,MAAM,GAAG,CAAC,IAAIX,uBAAuB,CAACC,YAAY,EAAEqK,OAAO,CAACtE,eAAe,IAAI,EAAE,CAAC,gBACvG1H,OAAA;0BAAK6K,SAAS,EAAC,MAAM;0BAAAC,QAAA,gBACnB9K,OAAA;4BAAI6K,SAAS,EAAC,0CAA0C;4BAAAC,QAAA,EAAC;0BAAkB;4BAAAS,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAI,CAAC,eAChF1L,OAAA;4BAAK6K,SAAS,EAAC,WAAW;4BAAAC,QAAA,EACvBuD,eAAe,CAACzF,GAAG,CAAC,CAAClG,MAAM,EAAEsI,KAAK,kBACjChL,OAAA,CAACH,aAAa;8BAAa6C,MAAM,EAAEA;4BAAiB,GAAhCsI,KAAK;8BAAAO,QAAA,EAAAC,YAAA;8BAAAC,UAAA;8BAAAC,YAAA;4BAAA,OAA6B,CACvD;0BAAC;4BAAAH,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OACC,CAAC;wBAAA;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACH,CAAC,GACJ,IAAI;sBACV,CAAC,EAAE,CAAC,eAEJ1L,OAAA;wBAAK6K,SAAS,EAAC,oCAAoC;wBAAAC,QAAA,gBACjD9K,OAAA;0BAAG6K,SAAS,EAAC,0CAA0C;0BAAAC,QAAA,EAAC;wBAAQ;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,eACpE1L,OAAA,CAACoK,UAAU;0BAACC,KAAK,EAAEwC,mBAAoB;0BAACvC,UAAU,EAAE;wBAAE;0BAAAiB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACtD,CAAC;oBAAA,GAtDC,mBAAmB;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAuDvB,CACP,CAAC;;oBAEH;kBACA,CAAC,MAAM,IAAIsB,iBAAiB,EAAE;oBAC5Be,YAAY,GAAG,cAAc;;oBAE7B;oBACA,MAAMO,YAAY,GAAGxB,kBAAkB,CAACzK,MAAM,GAAG,CAAC,GAAG,oBAAoB,GAAG,mBAAmB;oBAE/FyL,UAAU,CAACpF,IAAI,cACb1I,OAAA;sBAA4B6K,SAAS,EAAC,kGAAkG;sBAAAC,QAAA,gBACtI9K,OAAA;wBAAI6K,SAAS,EAAC,6DAA6D;wBAAAC,QAAA,EAAC;sBAE5E;wBAAAS,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAAI,CAAC,eACL1L,OAAA,CAACN,iBAAiB;wBAChBiH,OAAO,EAAEqF,OAAO,CAACrE,cAAc,IAAI,EAAG;wBACtCxE,KAAK,EAAExB,YAAa;wBACpBqM,KAAK,EAAEhC,OAAO,CAAC7E,SAAU;wBACzBJ,MAAM,EAAEiF,OAAO,CAACjF;sBAAO;wBAAAwE,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACxB,CAAC,eACF1L,OAAA;wBAAK6K,SAAS,EAAC,qCAAqC;wBAAAC,QAAA,gBAClD9K,OAAA;0BAAG6K,SAAS,EAAC,2CAA2C;0BAAAC,QAAA,EAAC;wBAAQ;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,eACrE1L,OAAA,CAACoK,UAAU;0BAACC,KAAK,EAAEyC,kBAAmB;0BAACxC,UAAU,EAAE;wBAAE;0BAAAiB,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAE,CAAC;sBAAA;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OACrD,CAAC;oBAAA,GAbC,kBAAkB;sBAAAH,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OActB,CACP,CAAC;;oBAEH;kBACA,CAAC,MAAM,IAAIuB,cAAc,EAAE;oBAAA,IAAAsB,kBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA,EAAAC,mBAAA;oBACzBb,YAAY,GAAG,KAAK;oBAEpB,MAAMc,SAAS,GAAG7C,OAAO,CAAC7E,SAAS,IAAI,QAAQ;oBAC/C,MAAM2H,SAAS,GAAG,CAAAP,kBAAA,GAAAvC,OAAO,CAAC7E,SAAS,cAAAoH,kBAAA,eAAjBA,kBAAA,CAAmBxM,QAAQ,CAAC,SAAS,CAAC,GAAG,IAAI,GAC/C,CAAAyM,mBAAA,GAAAxC,OAAO,CAAC7E,SAAS,cAAAqH,mBAAA,eAAjBA,mBAAA,CAAmBzM,QAAQ,CAAC,MAAM,CAAC,GAAG,GAAG,GACzC,CAAA0M,mBAAA,GAAAzC,OAAO,CAAC7E,SAAS,cAAAsH,mBAAA,eAAjBA,mBAAA,CAAmB1M,QAAQ,CAAC,UAAU,CAAC,GAAG,IAAI,GAC9C,CAAA2M,mBAAA,GAAA1C,OAAO,CAAC7E,SAAS,cAAAuH,mBAAA,eAAjBA,mBAAA,CAAmB3M,QAAQ,CAAC,MAAM,CAAC,GAAG,IAAI,GAC1C,CAAA4M,mBAAA,GAAA3C,OAAO,CAAC7E,SAAS,cAAAwH,mBAAA,eAAjBA,mBAAA,CAAmB5M,QAAQ,CAAC,QAAQ,CAAC,GAAG,IAAI,GAC5C,CAAA6M,mBAAA,GAAA5C,OAAO,CAAC7E,SAAS,cAAAyH,mBAAA,eAAjBA,mBAAA,CAAmB7M,QAAQ,CAAC,aAAa,CAAC,GAAG,IAAI,GAAG,IAAI;;oBAExE;oBACA,IAAI,CAACb,eAAe,IAAI,CAAC8K,OAAO,CAAC9E,OAAO,EAAE;sBAC1C4G,UAAU,CAACpF,IAAI,cACb1I,OAAA;wBAAwB6K,SAAS,EAAC,oGAAoG;wBAAAC,QAAA,gBACpI9K,OAAA;0BAAI6K,SAAS,EAAC,8DAA8D;0BAAAC,QAAA,GACzEgE,SAAS,EAAC,uBAAqB,EAACD,SAAS;wBAAA;0BAAAtD,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxC,CAAC,eACL1L,OAAA,CAACN,iBAAiB;0BAChBiH,OAAO,EAAEqF,OAAO,CAACrF,OAAO,IAAI,mEAAoE;0BAChGxD,KAAK,EAAExB,YAAa;0BACpBqM,KAAK,EAAEhC,OAAO,CAAC7E,SAAS,IAAI,QAAS;0BACrCJ,MAAM,EAAEiF,OAAO,CAACjF;wBAAO;0BAAAwE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxB,CAAC,eACF1L,OAAA;0BAAK6K,SAAS,EAAC,sCAAsC;0BAAAC,QAAA,eACnD9K,OAAA;4BAAG6K,SAAS,EAAC,gCAAgC;4BAAAC,QAAA,EAAC;0BAE9C;4BAAAS,QAAA,EAAAC,YAAA;4BAAAC,UAAA;4BAAAC,YAAA;0BAAA,OAAG;wBAAC;0BAAAH,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACD,CAAC;sBAAA,GAdC,cAAc;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAelB,CACP,CAAC;oBACD;;oBAEF;kBACA,CAAC,MAAM;oBACL;oBACAqC,YAAY,GAAG,YAAY;;oBAE3B;oBACA,IAAI,CAAC7M,eAAe,IAAI,CAAC8K,OAAO,CAAC9E,OAAO,EAAE;sBAC1C4G,UAAU,CAACpF,IAAI,cACb1I,OAAA;wBAAsB6K,SAAS,EAAC,sFAAsF;wBAAAC,QAAA,gBACpH9K,OAAA;0BAAG6K,SAAS,EAAC,4BAA4B;0BAAAC,QAAA,EAAC;wBAAgB;0BAAAS,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OAAG,CAAC,eAC9D1L,OAAA,CAACN,iBAAiB;0BAChBiH,OAAO,EAAEqF,OAAO,CAACrF,OAAO,IAAI,sGAAuG;0BACnIxD,KAAK,EAAExB,YAAa;0BACpBqM,KAAK,EAAEhC,OAAO,CAAC7E,SAAS,IAAI,QAAS;0BACrCJ,MAAM,EAAEiF,OAAO,CAACjF;wBAAO;0BAAAwE,QAAA,EAAAC,YAAA;0BAAAC,UAAA;0BAAAC,YAAA;wBAAA,OACxB,CAAC;sBAAA,GAPK,YAAY;wBAAAH,QAAA,EAAAC,YAAA;wBAAAC,UAAA;wBAAAC,YAAA;sBAAA,OAQhB,CACP,CAAC;oBACD;kBACF;;kBAEA;kBACA,IAAIoC,UAAU,CAACzL,MAAM,GAAG,CAAC,EAAE;oBACzB,oBACErC,OAAA;sBAAK6K,SAAS,EAAC,MAAM;sBAAAC,QAAA,EAClBgD;oBAAU;sBAAAvC,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACR,CAAC;kBAEV;;kBAEA;kBACA7I,OAAO,CAACkM,IAAI,CAAC,oCAAoC,CAAC;kBAClD,oBACE/O,OAAA;oBAAK6K,SAAS,EAAC,sFAAsF;oBAAAC,QAAA,gBACnG9K,OAAA;sBAAG6K,SAAS,EAAC,4BAA4B;sBAAAC,QAAA,EAAC;oBAAe;sBAAAS,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OAAG,CAAC,eAC7D1L,OAAA,CAACN,iBAAiB;sBAChBiH,OAAO,EAAEqF,OAAO,CAACrF,OAAO,IAAI,iDAAkD;sBAC9ExD,KAAK,EAAExB,YAAa;sBACpBqM,KAAK,EAAEhC,OAAO,CAAC7E,SAAS,IAAI,QAAS;sBACrCJ,MAAM,EAAEiF,OAAO,CAACjF;oBAAO;sBAAAwE,QAAA,EAAAC,YAAA;sBAAAC,UAAA;sBAAAC,YAAA;oBAAA,OACxB,CAAC;kBAAA;oBAAAH,QAAA,EAAAC,YAAA;oBAAAC,UAAA;oBAAAC,YAAA;kBAAA,OACC,CAAC;gBAEV,CAAC,EAAE;cAAC;gBAAAH,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OACD,CACN;YAAA;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACE;UAAC,GA/PDM,OAAO,CAACjG,EAAE;YAAAwF,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAgQZ,CACN,CAAC,eACF1L,OAAA;YAAKgP,GAAG,EAAE1N,cAAe;YAAC2N,KAAK,EAAE;cAAEC,KAAK,EAAE,MAAM;cAAEC,KAAK,EAAE;YAAO;UAAE;YAAA5D,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OAAE,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAClE;MACN;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACE,CAAC,eAGN1L,OAAA;QAAK6K,SAAS,EAAE,2GAA2G3K,WAAW,GAAG,mBAAmB,GAAG,QAAQ,EAAG;QAAA4K,QAAA,gBACxK9K,OAAA;UAAMoP,QAAQ,EAAEnJ,iBAAkB;UAAC4E,SAAS,EAAC,6BAA6B;UAAAC,QAAA,gBACxE9K,OAAA;YAAK6K,SAAS,EAAC,sBAAsB;YAAAC,QAAA,eACnC9K,OAAA;cACEqP,IAAI,EAAC,MAAM;cACXC,KAAK,EAAE1O,KAAM;cACb2O,QAAQ,EAAGrJ,CAAC,IAAK;gBACf,MAAMsJ,QAAQ,GAAGtJ,CAAC,CAACgF,MAAM,CAACoE,KAAK;gBAC/BzO,QAAQ,CAAC2O,QAAQ,CAAC;gBAClB;cACF,CAAE;cACFC,WAAW,EAAC,+CAA+C;cAC3D5E,SAAS,EAAC,wJAAwJ;cAClK6E,QAAQ,EAAE5O,YAAa;cACvB,cAAW;YAAe;cAAAyK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC3B;UAAC;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACC,CAAC,eAEN1L,OAAA;YAAK6K,SAAS,EAAC,6BAA6B;YAAAC,QAAA,gBAC1C9K,OAAA,CAACR,WAAW;cACVmQ,YAAY,EAAE3O,cAAe;cAC7B4O,aAAa,EAAGC,OAAO,IAAK5O,iBAAiB,CAAC4O,OAAO,CAAE;cACvDC,SAAS,EAAEhP;YAAa;cAAAyK,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OACzB,CAAC,eACF1L,OAAA;cACEqP,IAAI,EAAC,QAAQ;cACbK,QAAQ,EAAE5O,YAAY,IAAI,CAACF,KAAK,CAAC8E,IAAI,CAAC,CAAE;cACxCmF,SAAS,EAAC,uMAAuM;cACjNO,KAAK,EAAC,cAAc;cAAAN,QAAA,eAEpB9K,OAAA;gBAAA8K,QAAA,EAAOhK,YAAY,GAAG,YAAY,GAAG;cAAM;gBAAAyK,QAAA,EAAAC,YAAA;gBAAAC,UAAA;gBAAAC,YAAA;cAAA,OAAO;YAAC;cAAAH,QAAA,EAAAC,YAAA;cAAAC,UAAA;cAAAC,YAAA;YAAA,OAC7C,CAAC;UAAA;YAAAH,QAAA,EAAAC,YAAA;YAAAC,UAAA;YAAAC,YAAA;UAAA,OACN,CAAC;QAAA;UAAAH,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OACF,CAAC,eACK1L,OAAA;UAAK6K,SAAS,EAAC,wCAAwC;UAAAC,QAAA,GAAC,iBACnD,EAAC,EAAA1K,qBAAA,GAAAX,kBAAkB,CAACmG,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,EAAE,KAAK/E,cAAc,CAAC,cAAAZ,qBAAA,uBAArDA,qBAAA,CAAuD0F,IAAI,KAAI9E,cAAc;QAAA;UAAAuK,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,OAC1F,CAAC;MAAA;QAAAH,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OACH,CAAC,EAELpL,QAAQ,CAAC+B,MAAM,GAAG,CAAC,iBAAIrC,OAAA;QAAK6K,SAAS,EAAC;MAAM;QAAAU,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,OAAM,CAAC;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,OACjD,CAAC;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OACH,CAAC;AAEV;AAOA,SAASqE,GAAGA,CAAC;EAAE7P,WAAW;EAAEC;AAAyB,CAAC,EAAE;EACtD,oBACEH,OAAA,CAACC,aAAa;IAACC,WAAW,EAAEA,WAAY;IAACC,cAAc,EAAEA;EAAe;IAAAoL,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,OAAE,CAAC;AAE/E;AAEA,eAAeqE,GAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module"}