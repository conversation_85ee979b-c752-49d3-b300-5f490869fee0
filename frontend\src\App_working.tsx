import React, { useState, useRef, useEffect } from 'react';
import './App.css';
import { sendQuery } from './services/api';
import LLMSelector, { DEFAULT_LLM_MODELS } from './components/ui/LLMSelector';
import InteractiveAnswer from './components/ui/InteractiveAnswer';
import ChatSidebar from './components/chat/ChatSidebar';
import TrainLoader from './components/ui/TrainLoader';
import VisualContent from './components/ui/VisualContent';
import { useChatContext } from './contexts/ChatContext';
import { ChatSession, ChatMessage } from './services/supabase';

interface Source {
  source_type: string;
  filename?: string;
  page?: number;
  url?: string;
  link?: string; // For document viewer links
  name?: string; // For display name
  // Visual content fields
  content_type?: string;  // "text", "table", "image", "chart_diagram"
  visual_content?: Record<string, any>;  // Visual content metadata
  storage_url?: string;  // URL for stored visual content
  display_type?: string;  // "text", "html_table", "image", "base64_image"
}

interface ChatInterfaceProps {
  sidebarOpen: boolean;
  setSidebarOpen: React.Dispatch<React.SetStateAction<boolean>>;
}

function ChatInterface({ sidebarOpen, setSidebarOpen }: ChatInterfaceProps) {
  const {
    currentSession,
    messages,
    createNewChat,
    loadChatSession,
    addMessage,
    updateMessage,
    clearCurrentChat
  } = useChatContext();

  const [input, setInput] = useState<string>('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [activeLLMModel, setActiveLLMModel] = useState('gemini-2.0-flash'); // Default LLM model
  const [showTrainLoader, setShowTrainLoader] = useState(false);
  const [currentSearchStage, setCurrentSearchStage] = useState<'initializing' | 'searching_documents' | 'searching_websites' | 'generating_answer' | 'complete'>('initializing');
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Scroll to bottom of chat whenever messages change
  useEffect(() => {
    if (messagesEndRef.current) {
      messagesEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [messages]);

  // Helper function to determine if visual content should be shown
  const shouldShowVisualContent = (userQuestion: string, documentAnswer: string): boolean => {
    const userQuery = userQuestion.toLowerCase();

    // Always show visual content when user asks for images
    if (userQuery.includes('image')) {
      return true;
    }
    
    // Detect different types of requests
    const askedForImages = userQuery.includes('image') || userQuery.includes('picture') || userQuery.includes('diagram') || userQuery.includes('chart');
    const askedForTableData = (userQuery.includes('table') || userQuery.includes('quotation')) && 
                             (userQuery.includes('give me') || userQuery.includes('show me table') || userQuery.includes('get') || userQuery.includes('provide'));
    const askedToShowImages = userQuery.includes('show me image') || userQuery.includes('display image') || userQuery.includes('see image');
    
    const answerLower = documentAnswer?.toLowerCase() || '';
    const hasTableInAnswer = answerLower.includes('<table>') || answerLower.includes('|') || 
                            (answerLower.includes('table') && answerLower.length > 200);
    const hasImageDescription = answerLower.includes('image') || answerLower.includes('figure') || answerLower.includes('diagram');
    const answerHasContent = answerLower.length > 50;
    
    // ALWAYS show visual content if user specifically asks to see images/visuals
    if (askedToShowImages || (askedForImages && (userQuery.includes('show') || userQuery.includes('display')))) {
      return true;
    }

    // NEVER show if user asked for table data (they want the actual data, not images)
    if (askedForTableData && !askedForImages) {
      return false;
    }
    
    // For other cases, show only if answer doesn't already provide adequate information
    const shouldShow = !hasTableInAnswer && // Don't show if answer already has table data
                      !hasImageDescription && // Don't show if answer already describes images well
                      (
                        (askedForImages && !answerHasContent) || // Show images if user asked and answer is short
                        (!answerHasContent && answerLower.length < 50) // Show for very short answers
                      );
    
    return shouldShow;
  };

  // Handle command shortcuts in the textbox
  const handleCommandShortcut = (input: string) => {
    // Check if the input is a command
    if (input.startsWith('/')) {
      const command = input.split(' ')[0].toLowerCase();

      // Command: /model <model-name>
      if (command === '/model') {
        const modelArg = input.substring(7).trim();
        const matchedModel = DEFAULT_LLM_MODELS.find(m =>
          m.name.toLowerCase().includes(modelArg.toLowerCase()) ||
          m.id.toLowerCase().includes(modelArg.toLowerCase())
        );

        if (matchedModel && matchedModel.enabled) {
          setActiveLLMModel(matchedModel.id);
          setInput('');
          return 'processed';
        }
      }

      // Command: /reset or /clear - clear chat history
      else if (command === '/reset' || command === '/clear') {
        clearCurrentChat();
        setInput('');
        return 'processed';
      }
    }

    return 'not_processed';
  };

  const handleSendMessage = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!input.trim() || isSubmitting) return;

    // Handle command shortcuts like /reset, /model, etc.
    if (input.startsWith('/')) {
      const result = handleCommandShortcut(input);
      if (result === 'processed') {
        setInput('');
        return;
      }
      // If not processed as a command, continue as a regular message
    }

    return await sendUserMessage(input);
  };

  const sendUserMessage = async (messageText: string) => {
    // Create new chat session if none exists
    if (!currentSession) {
      await createNewChat();
    }

    const userMessage: ChatMessage = {
      id: `user-${Date.now()}`,
      content: messageText,
      sender: 'user',
      timestamp: new Date().toISOString(),
      chatId: currentSession?.id || 'temp',
    };

    addMessage(userMessage);
    setInput('');

    const messageId = Date.now();
    const tempAiMessage: ChatMessage = {
      id: `ai-${messageId}`,
      content: '',
      sender: 'ai',
      loading: true,
      timestamp: new Date().toISOString(),
      llm_model: activeLLMModel,
      chatId: currentSession?.id || 'temp',
    };

    addMessage(tempAiMessage);
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });

    setIsSubmitting(true);
    setShowTrainLoader(true);
    setCurrentSearchStage('initializing');

    try {
      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });

      // Simulate search progress updates with train loader
      setTimeout(() => setCurrentSearchStage('searching_documents'), 500);
      setTimeout(() => setCurrentSearchStage('searching_websites'), 2000);
      setTimeout(() => setCurrentSearchStage('generating_answer'), 4000);

      const response = await sendQuery(messageText, activeLLMModel);

      // Create the AI message based on strict priority logic
      const aiMessage: ChatMessage = {
        id: `ai-${messageId}`,
        content: response.answer,
        document_answer: response.document_answer,
        website_answer: response.website_answer,
        llm_model: response.llm_model || activeLLMModel,
        sender: 'ai',
        sources: response.sources,
        document_sources: response.document_sources,
        website_sources: response.website_sources,
        chatId: currentSession?.id || 'temp',
        llmFallbackUsed: response.llmFallbackUsed,
      };

      updateMessage(tempAiMessage.id, aiMessage);

      messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
    } catch (error) {
      console.error('Error sending message:', error);

      // Provide more helpful error message without the specific query
      const errorMessage: ChatMessage = {
        id: `ai-${messageId}`,
        content: `I'm sorry, I encountered an issue while trying to answer your question. This might be due to backend connectivity issues or a problem with processing your request. Please try again or rephrase your question.`,
        sender: 'ai',
        chatId: currentSession?.id || 'temp',
        llmFallbackUsed: true,
      };

      updateMessage(tempAiMessage.id, errorMessage);
    } finally {
      setIsSubmitting(false);
      setShowTrainLoader(false);
      setCurrentSearchStage('complete');
    }
  };

  const processDocumentSources = (sources?: Array<Source | string>) => {
    if (!sources || sources.length === 0) return [];

    // Group by filename to avoid repetition
    const groupedSources: { [key: string]: { filename: string; pages: number[] } } = {};

    sources.forEach(source => {
      let filename: string;
      let page: number;

      if (typeof source === 'string') {
        // Parse string format like "MaintenanceManual.pdf – Page 3"
        const match = source.match(/([^–]+)(?:\s*–\s*Page\s*(\d+))?/i);
        filename = match ? match[1].trim() : source;
        page = match && match[2] ? parseInt(match[2]) : 1;
      } else {
        filename = source.name || source.filename || "Unknown Document";
        page = source.page || 1;
        }

      if (!groupedSources[filename]) {
        groupedSources[filename] = { filename, pages: [] };
      }

      if (!groupedSources[filename].pages.includes(page)) {
        groupedSources[filename].pages.push(page);
      }
    });

    // Convert to display format with viewer links
    return Object.values(groupedSources).map(group => {
      const sortedPages = group.pages.sort((a, b) => a - b);
      const pageText = sortedPages.length === 1
        ? `Page ${sortedPages[0]}`
        : `Pages ${sortedPages.join(', ')}`;

      // Create link for document viewer that opens at exact page number
      return {
        text: `${group.filename} – ${pageText}`,
        link: `/viewer?file=${encodeURIComponent(group.filename)}&page=${sortedPages[0]}`,
        isDocument: true
      };
    });
  };

  const processWebsiteSources = (sources?: Array<Source | string>) => {
    if (!sources || sources.length === 0) return [];

    // Remove duplicates and format
    const uniqueUrls = new Set<string>();
    const processed: Array<{ text: string; link: string; isDocument?: boolean }> = [];

    sources.forEach(source => {
      let url: string;
      let displayText: string;

      if (typeof source === 'string') {
        url = source.startsWith('http') ? source : `https://${source}`;
          try {
          const urlObj = new URL(url);
          displayText = urlObj.hostname.replace(/^www\./, '');
          } catch {
          displayText = source;
        }
      } else {
        url = source.url || 'https://railgpt.indianrailways.gov.in';
        try {
          const urlObj = new URL(url);
          displayText = urlObj.hostname.replace(/^www\./, '');
        } catch {
          displayText = url;
        }
      }

      if (!uniqueUrls.has(url)) {
        uniqueUrls.add(url);
        processed.push({
          text: displayText,
          link: url,
          isDocument: false  // Mark as website source
        });
        }
    });

    return processed; // Return all website sources
  };
} // Added closing bracket for function and component
