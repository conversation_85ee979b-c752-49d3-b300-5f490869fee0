import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkGfm from 'remark-gfm';
import SourceCitation from './SourceCitation';
import { Loader2 } from 'lucide-react';

export interface ChatMessageProps {
  id: string;
  content: string;
  sender: 'user' | 'ai';
  loading?: boolean;
  sources?: Array<any>;
  document_sources?: Array<any>;
  website_sources?: Array<any>;
  document_answer?: string;
  website_answer?: string;
  llmFallbackUsed?: boolean;
  llm_model?: string;
}

interface Source {
  source_type: string;
  filename?: string;
  name?: string;
  page?: number;
  pages?: number[];
  url?: string;
  link?: string;
  content_type?: string;
  visual_content?: Record<string, any>;
  storage_url?: string;
  display_type?: string;
}

const ChatMessage: React.FC<ChatMessageProps> = ({
  content,
  sender,
  loading,
  sources,
  document_sources,
  website_sources,
  document_answer,
  website_answer,
  llmFallbackUsed,
  llm_model
}) => {
  // Determine if it's a user or AI message
  const isUser = sender === 'user';

  // Set background color based on sender
  const bgColor = isUser ? 'bg-blue-50' : 'bg-gray-50';

  // Set text alignment based on sender
  const textAlign = isUser ? 'text-right' : 'text-left';

  // Check what type of answer we have
  const hasDocumentAnswer = document_answer && document_sources && document_sources.length > 0;
  const hasWebsiteAnswer = website_answer && website_sources && website_sources.length > 0;
  const hasLLMFallback = llmFallbackUsed && content && !hasDocumentAnswer && !hasWebsiteAnswer;

  // Process document sources to show deduplicated pages
  const processDocumentSources = (sources: any[]) => {
    const sourceMap = new Map<string, { filename: string; pages: Set<number> }>();

    sources.forEach(source => {
      const filename = source.filename || source.name || 'Unknown Document';
      const page = source.page || source.pages?.[0];

      if (!sourceMap.has(filename)) {
        sourceMap.set(filename, { filename, pages: new Set<number>() });
      }

      if (page && typeof page === 'number') {
        sourceMap.get(filename)!.pages.add(page);
      }
    });

    return Array.from(sourceMap.values()).map(source => ({
      filename: source.filename,
      pages: Array.from(source.pages).sort((a: number, b: number) => a - b)
    }));
  };

  // Process website sources to show unique URLs
  const processWebsiteSources = (sources: any[]) => {
    const urlSet = new Set();
    return sources.filter(source => {
      const url = source.url;
      if (url && !urlSet.has(url)) {
        urlSet.add(url);
        return true;
      }
      return false;
    }).slice(0, 3); // Max 3 URLs as per specification
  };

  // Render visual content (images, tables, etc.)
  const renderVisualContent = (sources: any[]) => {
    const visualSources = sources.filter(source => source.visual_content);
    
    if (visualSources.length === 0) return null;

    return (
      <div className="mt-4">
        <h5 className="text-sm font-semibold mb-3">📊 Visual Content:</h5>
        <div className="space-y-3">
          {visualSources.map((source, index) => (
            <div key={index} className="border rounded p-2">
              {source.content_type === 'image' && source.storage_url && (
                <img 
                  src={source.storage_url} 
                  alt={`Visual content from ${source.filename}`}
                  className="max-w-full h-auto rounded"
                />
              )}
              {source.content_type === 'table' && source.visual_content && (
                <div 
                  className="overflow-x-auto"
                  dangerouslySetInnerHTML={{ __html: source.visual_content.html || 'Table not found' }}
                />
              )}
              {!source.storage_url && source.content_type === 'image' && (
                <div className="text-gray-500 italic">Image not found</div>
              )}
            </div>
          ))}
        </div>
      </div>
    );
  };

  return (
    <div className={`p-4 rounded-lg mb-4 ${bgColor}`}>
      <div className={`${textAlign}`}>
        <div className="font-semibold mb-1">
          {isUser ? 'You' : 'RailGPT'}
        </div>

        {loading ? (
          <div className="flex items-center space-x-2">
            <Loader2 className="h-4 w-4 animate-spin" />
            <span>Generating response...</span>
          </div>
        ) : (
          <div className="space-y-4">
            {/* Document Answer Card */}
            {hasDocumentAnswer && (
              <div className="p-4 bg-blue-50 rounded-lg border border-blue-200 shadow-sm">
                <h4 className="font-semibold text-blue-800 text-sm mb-3 flex items-center">
                  📄 Answer Found in Document
                </h4>
                <div className="markdown-content mb-3">
                  <ReactMarkdown remarkPlugins={[remarkGfm]}>
                    {document_answer}
                  </ReactMarkdown>
                </div>
                
                {/* Visual Content */}
                {renderVisualContent(document_sources)}
                
                {/* Document Sources */}
                <div className="mt-3 pt-3 border-t border-blue-200">
                  <p className="text-xs text-blue-700 font-semibold mb-1">Sources:</p>
                  <div className="text-sm text-blue-600">
                    {processDocumentSources(document_sources).map((source, index) => (
                      <div key={index} className="mb-1">
                        <button
                          onClick={() => {
                            const firstPage = source.pages[0] || 1;
                            window.open(`/viewer?doc=${encodeURIComponent(source.filename)}&page=${firstPage}`, '_blank');
                          }}
                          className="text-blue-600 hover:underline bg-transparent border-none cursor-pointer"
                        >
                          {source.filename}
                        </button>
                        {source.pages.length > 0 && (
                          <span className="text-gray-600 ml-1">
                            - Page{source.pages.length > 1 ? 's' : ''} {source.pages.join(', ')}
                          </span>
                        )}
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Website Answer Card */}
            {hasWebsiteAnswer && (
              <div className="p-4 bg-green-50 rounded-lg border border-green-200 shadow-sm">
                <h4 className="font-semibold text-green-800 text-sm mb-3 flex items-center">
                  🌐 Answer Found in Extracted Website{website_sources.length > 1 ? 's' : ''}
                </h4>
                <div className="markdown-content mb-3">
                  <ReactMarkdown remarkPlugins={[remarkGfm]}>
                    {website_answer}
                  </ReactMarkdown>
                </div>
                
                {/* Website Sources */}
                <div className="mt-3 pt-3 border-t border-green-200">
                  <p className="text-xs text-green-700 font-semibold mb-1">Sources:</p>
                  <div className="text-sm text-green-600">
                    {processWebsiteSources(website_sources).map((source, index) => (
                      <div key={index} className="mb-1">
                        <button
                          onClick={() => window.open(source.url, '_blank')}
                          className="text-green-600 hover:underline bg-transparent border-none cursor-pointer break-all"
                        >
                          {source.url}
                        </button>
                      </div>
                    ))}
                    {website_sources.length > 3 && (
                      <div className="text-gray-600 text-xs mt-1">
                        + {website_sources.length - 3} more sources
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* LLM Fallback Card */}
            {hasLLMFallback && (
              <div className="p-4 bg-yellow-50 rounded-lg border border-yellow-200 shadow-sm">
                <h4 className="font-semibold text-yellow-800 text-sm mb-3 flex items-center">
                  🤖 Answer Generated by {llm_model || 'AI'}
                </h4>
                <div className="markdown-content mb-3">
                  <ReactMarkdown remarkPlugins={[remarkGfm]}>
                    {content}
                  </ReactMarkdown>
                </div>
                <div className="text-xs text-yellow-700 italic">
                  ⚠️ No matching info in uploaded documents or websites.
                </div>
              </div>
            )}

            {/* Generic content display if no specific answer cards */}
            {!hasDocumentAnswer && !hasWebsiteAnswer && !hasLLMFallback && content && (
              <div className="markdown-content">
                <ReactMarkdown remarkPlugins={[remarkGfm]}>
                  {content}
                </ReactMarkdown>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};

export default ChatMessage;
